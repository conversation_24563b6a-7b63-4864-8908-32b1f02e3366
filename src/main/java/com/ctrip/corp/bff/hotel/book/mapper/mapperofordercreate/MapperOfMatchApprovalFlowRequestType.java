package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookHotelInfoEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckOverStandardRcInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.FloatPriceOverDetailInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.PriceType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyApprovalBillResultType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyPassengerResultType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.entity.AccountSettingType;
import com.ctrip.corp.agg.hotel.tmc.entity.FinalPolicyType;
import com.ctrip.corp.agg.hotel.tmc.entity.FinalTravelPolicyType;
import com.ctrip.corp.approve.ws.contract.*;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfAccountInfoConfig;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.HotelDateRangeUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.AggBookPriceResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.CouponToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ServiceChargeResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.common.RcToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.CostCenterKeyDto;
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.SaveCostCenterInputItem;
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.SaveCostCenterInputVo;
import com.ctrip.corp.bff.framework.specific.common.enums.CostCenterTypeEnum;
import com.ctrip.corp.bff.framework.specific.common.utils.CostCenterKeyUtils;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.MapString;
import com.ctrip.corp.bff.framework.template.entity.PosEnum;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.Approver;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CostCenterInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCContent;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RcTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.MathUtils;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfMatchApprovalFlow;
import com.ctrip.corp.bff.hotel.book.contract.BookModeInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import com.ctrip.corp.foundation.common.constant.StringConstants;
import com.ctrip.corp.foundation.common.util.Null;
import com.ctrip.corp.foundation.common.util.StringUtilsExt;
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;
import com.ctrip.framework.foundation.Foundation;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/23 18:31
 */
@Component public class MapperOfMatchApprovalFlowRequestType extends
    AbstractMapper<Tuple1<WrapperOfMatchApprovalFlow>, MatchApprovalFlowRequestType> {

    private static final String APPROVALTYPE = "B";

    /**
     * 因公 因私
     */
    private static final int TRAVEL_REASON_PUBLIC = 1;

    private static final int TRAVEL_REASON_PRIVATE = 2;

    public static final String BOOKING_CHANNEL_OFFLINE = "OFFLINE";
    public static final String BOOKING_CHANNEL_ONLINE = "ONLINE";
    public static final String BOOKING_CHANNEL_APP = "APP";

    private static final String CHANNEL = "channel";

    private static final String SHARE_ROOM = "SHARE_ROOM";

    private static final String OTHER_ORDER_ROOM = "OTHER_ORDER_ROOM";

    /**
     * 未超过基本差标上限
     */
    public static final String BELOW_BASE_STANDARD_UPPER_LIMIT = "BELOW_BASE_STANDARD_UPPER_LIMIT";
    /**
     * 超过基本差标上限
     */
    public static final String OVER_BASE_STANDARD_UPPER_LIMIT = "OVER_BASE_STANDARD_UPPER_LIMIT";
    /**
     * 超过浮动后最终差标上限
     */
    public static final String OVER_FINAL_STANDARD_UPPER_LIMIT = "OVER_FINAL_STANDARD_UPPER_LIMIT";

    public static final String CORP_PAY = "CORP_PAY";
    public static final String PERSONAL_PAY = "PERSONAL_PAY";
    public static final String MIX_PAY = "MIX_PAY";

    public static final String CORP = "C";
    public static final String PERSONAL = "P";
    /**
     * 一级审批人
     */
    public static final int APPROVAL_LEVEL_1 = 1;
    /**
     * 二级审批人
     */
    public static final int APPROVAL_LEVEL_2 = 2;

    private static final String OVERSEA = "O";
    private static final String DOMESTIC = "H";
    public static final List<String> COST_CENTERS = Arrays.asList("CC1", "CC2", "CC3", "CC4", "CC5", "CC6");

    /**
     * 转换
     * <p>
     * 节点描述: http://conf.ctripcorp.com/pages/viewpage.action?pageId=2607653487
     * <p>
     * orderCreateRequestType 创建订单 http://contract.mobile.flight.ctripcorp.com/#/operation-detail/26034/4/orderCreate?lang=zh-CN
     * checkAvailContextResponseType 可定检查 http://contract.mobile.flight.ctripcorp.com/#/operation-detail/2558/163/queryCheckAvailContext?lang=zh-CN
     * checkTravelPolicyResponseType  http://contract.mobile.flight.ctripcorp.com/#/operation-detail/324/44/checkTravelPolicy
     * getTravelPolicyContextResponseType http://contract.mobile.flight.ctripcorp.com/#/operation-detail/324/44/getTravelPolicyContext?lang=zh-CN
     *
     * @param tuple
     * @return matchApprovalFlowRequestType http://contract.mobile.flight.ctripcorp.com/#/operation-detail/2736/70/matchApprovalFlow?lang=zh-CN
     */
    @Override protected MatchApprovalFlowRequestType convert(
        Tuple1<WrapperOfMatchApprovalFlow> tuple) {
        WrapperOfMatchApprovalFlow wrapperOfMatchApprovalFlow = tuple.getT1();

        OrderCreateRequestType orderCreateRequestType = wrapperOfMatchApprovalFlow.getOrderCreateRequestType();
        // 请求头信息
        IntegrationSoaRequestType integrationSoaRequestType = orderCreateRequestType.getIntegrationSoaRequestType();

        // 可定反查信息
        QueryCheckAvailContextResponseType checkAvailContextResponseType =
            wrapperOfMatchApprovalFlow.getQueryCheckAvailContextResponseType();

        // 查询差标结果
        GetTravelPolicyContextResponseType getTravelPolicyContextResponse =
            wrapperOfMatchApprovalFlow.getGetTravelPolicyContextResponseType();

        // rc 结果
        CheckTravelPolicyResponseType checkTravelPolicyResponse =
            wrapperOfMatchApprovalFlow.getCheckTravelPolicyResponseType();
        ResourceToken resourceToken = wrapperOfMatchApprovalFlow.getResourceToken();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = wrapperOfMatchApprovalFlow.getBaseCheckAvailInfo();
        WrapperOfAccount.AccountInfo accountInfo = wrapperOfMatchApprovalFlow.getAccountInfo();
        SSOInfoQueryResponseType ssoInfoQueryResponseType = wrapperOfMatchApprovalFlow.getSsoInfoQueryResponseType();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig =
            wrapperOfMatchApprovalFlow.getQconfigOfCertificateInitConfig();
        GetCityBaseInfoResponseType getCityBaseInfoResponseType =
            wrapperOfMatchApprovalFlow.getGetCityBaseInfoResponseType();
        Map<String, StrategyInfo> strategyInfoMap =
            wrapperOfMatchApprovalFlow.getStrategyInfoMap();
        QConfigOfAccountInfoConfig qConfigOfAccountInfoConfig =
            wrapperOfMatchApprovalFlow.getQConfigOfAccountInfoConfig();

        MatchApprovalFlowRequestType requestType = new MatchApprovalFlowRequestType();
        requestType.setApprovalType(APPROVALTYPE);
        requestType.setServerFrom(Foundation.app().getAppId());
        requestType.setSessionId(UUID.randomUUID().toString());
        requestType.setCompanyId(getCorpId(integrationSoaRequestType));
        requestType.setUid(getUserId(integrationSoaRequestType));
        requestType.setPolicyUserId(getPolicyUserId(orderCreateRequestType, accountInfo));
        requestType.setApproveObjInfos(
            buildApproveObjInfos(orderCreateRequestType, checkAvailContextResponseType, getTravelPolicyContextResponse,
                checkTravelPolicyResponse, resourceToken, checkAvailInfo, accountInfo, ssoInfoQueryResponseType,
                qconfigOfCertificateInitConfig, strategyInfoMap, getCityBaseInfoResponseType,
                qConfigOfAccountInfoConfig));

        return requestType;
    }

    @Override protected ParamCheckResult check(
        Tuple1<WrapperOfMatchApprovalFlow> tuple) {
        WrapperOfMatchApprovalFlow wrapperOfMatchApprovalFlow = tuple.getT1();
        OrderCreateRequestType orderCreateRequestType = wrapperOfMatchApprovalFlow.getOrderCreateRequestType();
        CheckTravelPolicyResponseType checkTravelPolicyResponseType =
            wrapperOfMatchApprovalFlow.getCheckTravelPolicyResponseType();
        ResourceToken resourceToken = wrapperOfMatchApprovalFlow.getResourceToken();
        OrderCreateToken orderCreateToken = wrapperOfMatchApprovalFlow.getOrderCreateToken();
        WrapperOfAccount.AccountInfo accountInfo = wrapperOfMatchApprovalFlow.getAccountInfo();
        // 匹配审批流之前要校验下rc是否还有没选的 有可能是前端bug或者开关变更 导致的导致rc框没弹出，需要拦截
        if (buildMissRc(orderCreateRequestType, checkTravelPolicyResponseType, resourceToken, orderCreateToken,
            accountInfo) && QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.RC_CHECK,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.MISS_RC_LIST,
                OrderCreateErrorEnum.MISS_RC_LIST.getErrorCode().toString());
        }
        return null;
    }

    /**
     * 是否缺失rc
     * 1.管控校验：需要对应的低价/协议/提前预定rc，但是没有选择
     * 2.包含重复预订二次提交标识+需要重复预订rc，但入参缺失重复预订rc
     *
     * @param requestType
     * @param checkTravelPolicyResponseType
     * @param resourceToken
     * @return
     */
    private boolean buildMissRc(OrderCreateRequestType requestType,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType, ResourceToken resourceToken,
        OrderCreateToken orderCreateToken, WrapperOfAccount.AccountInfo accountInfo) {
        if (checkTravelPolicyResponseType == null || checkTravelPolicyResponseType.getCheckRcResult() == null) {
            return false;
        }
        CheckOverStandardRcInfoType checkOverStandardRcInfoType =
            OrderCreateProcessorOfUtil.getCheckOverStandardRcInfoType(checkTravelPolicyResponseType,
                requestType.getHotelPayTypeInput(), resourceToken);
        if (BooleanUtils.isTrue(Boolean.TRUE.equals(checkOverStandardRcInfoType.isRequired()) && !Boolean.TRUE.equals(
            checkOverStandardRcInfoType.isSelected()))) {
            return true;
        }
        if (checkTravelPolicyResponseType.getCheckRcResult().getAgreementRc() != null) {
            if (BooleanUtils.isTrue(
                Boolean.TRUE.equals(checkTravelPolicyResponseType.getCheckRcResult().getAgreementRc().isRequired())
                    && !Boolean.TRUE.equals(
                    checkTravelPolicyResponseType.getCheckRcResult().getAgreementRc().isSelected()))) {
                return true;
            }
        }
        if (checkTravelPolicyResponseType.getCheckRcResult().getBookAheadRc() != null) {
            if (BooleanUtils.isTrue(
                Boolean.TRUE.equals(checkTravelPolicyResponseType.getCheckRcResult().getBookAheadRc().isRequired())
                    && !Boolean.TRUE.equals(
                    checkTravelPolicyResponseType.getCheckRcResult().getBookAheadRc().isSelected()))) {
                return true;
            }
        }
        if (orderCreateToken.containsContinueType(ContinueTypeConst.CONFLICT_ORDER) && StringUtils.equalsIgnoreCase(
            CommonConstant.OPEN, accountInfo.getRepeatBookingReason())) {
            if (CollectionUtil.isEmpty(requestType.getRcInfos())) {
                return true;
            }
            return requestType.getRcInfos().stream().noneMatch(this::buildConflictBookRC);
        }
        return false;
    }

    private boolean buildConflictBookRC(RCInput rcInput) {
        if (rcInput == null || StringUtil.isBlank(rcInput.getRcToken())) {
            return false;
        }
        RcToken rcToken = TokenParseUtil.parseToken(rcInput.getRcToken(), RcToken.class);
        if (rcToken == null) {
            return false;
        }
        return RcTypeEnum.getType(rcToken.getType()) == RcTypeEnum.CONFLICT_BOOK;
    }

    /**
     * 构建
     *
     * @param orderCreateRequestType
     * @return
     */
    private ApproveObjInfo buildApproveObjInfos(OrderCreateRequestType orderCreateRequestType,
        QueryCheckAvailContextResponseType checkAvailContextResponseType,
        GetTravelPolicyContextResponseType getTravelPolicyContextResponse,
        CheckTravelPolicyResponseType checkTravelPolicyResponse, ResourceToken resourceToken,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        WrapperOfAccount.AccountInfo accountInfo, SSOInfoQueryResponseType ssoInfoQueryResponseType,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap,
        GetCityBaseInfoResponseType getCityBaseInfoResponseType,
        QConfigOfAccountInfoConfig qConfigOfAccountInfoConfig) {

        ApproveObjInfo approveObjInfo = new ApproveObjInfo();
        approveObjInfo.setCorpBookingInfo(
            buildCorpBookingInfo(orderCreateRequestType, checkAvailContextResponseType, getTravelPolicyContextResponse,
                checkTravelPolicyResponse, resourceToken, checkAvailInfo, accountInfo, strategyInfoMap,
                getCityBaseInfoResponseType, qConfigOfAccountInfoConfig));
        approveObjInfo.setApproveObj(
            buildApproveObj(orderCreateRequestType, checkAvailInfo, qconfigOfCertificateInitConfig));
        // 降噪：蓝色空间不存在指定单点审批人场景
        if (orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getPos() == PosEnum.CHINA) {
            approveObjInfo.setAccreditorList(buildAccreditors(ssoInfoQueryResponseType));
        }

        return approveObjInfo;
    }

    /**
     * 用户指定审批人列表
     *
     * @return
     */
    private List<Accreditor> buildAccreditors(SSOInfoQueryResponseType ssoInfoQueryResponseType) {
        if (ssoInfoQueryResponseType == null || ssoInfoQueryResponseType.getSsoBaseInfo() == null
            || CollectionUtils.isEmpty(ssoInfoQueryResponseType.getSsoBaseInfo().getApprovers())) {
            return Collections.emptyList();
        }
        return buildAccreditor(ssoInfoQueryResponseType.getSsoBaseInfo().getApprovers().stream()
            .filter(t -> "1".equalsIgnoreCase(t.getLevel())).collect(Collectors.toList()).stream().findFirst()
            .orElse(new Approver()).getUid(), ssoInfoQueryResponseType.getSsoBaseInfo().getApprovers().stream()
            .filter(t -> "2".equalsIgnoreCase(t.getLevel())).collect(Collectors.toList()).stream().findFirst()
            .orElse(new Approver()).getUid());
    }

    /**
     * 构建审批人信息
     * @return
     */
    protected List<Accreditor> buildAccreditor(String confirmPersonUid1,
        String confirmPersonUid2) {
        List<Accreditor> list = new ArrayList<>();
        if (StringUtil.isNotEmpty(confirmPersonUid1)) {
            Accreditor to = new Accreditor();
            to.setLevel(APPROVAL_LEVEL_1);
            to.setUid(confirmPersonUid1);
            list.add(to);
        }
        if (StringUtil.isNotEmpty(confirmPersonUid2)) {
            Accreditor to = new Accreditor();
            to.setLevel(APPROVAL_LEVEL_2);
            to.setUid(confirmPersonUid2);
            list.add(to);
        }
        return list;
    }

    /**
     * 构建审批公共信息节点
     *
     * @param orderCreateRequestType
     * @return
     */
    protected ApproveObj buildApproveObj(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailContextResponse,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
        ApproveObj approveObj = new ApproveObj();

        if (orderCreateRequestType.getCostCenterInfo() != null && StringUtil.isNotBlank(
            orderCreateRequestType.getCostCenterInfo().getCostCenterJsonString())) {
            SaveCostCenterInputVo saveCostCenterInputVo =
                JsonUtil.fromJsonIgnoreCase(orderCreateRequestType.getCostCenterInfo().getCostCenterJsonString(),
                    SaveCostCenterInputVo.class);
            if (saveCostCenterInputVo != null && CollectionUtil.isNotEmpty(saveCostCenterInputVo.getItems())) {
                approveObj.setCostCenters(getCostCenterItems(saveCostCenterInputVo));
                approveObj.setProjectNames(getProjectNoItems(saveCostCenterInputVo));
                approveObj.setTravelPurposeNames(getTravelPurposeItems(saveCostCenterInputVo));
            }
        }

        // 蓝色空间成本中心匹配审批流降噪
        if (CollectionUtil.isNotEmpty(orderCreateRequestType.getCostCenterInputs())) {
            approveObj.setCostCenters(buildCostCenter(orderCreateRequestType.getCostCenterInputs()));
            approveObj.setProjectNames(buildProjectNames(orderCreateRequestType.getCostCenterInputs()));
            approveObj.setTravelPurposeNames(buildTravelPurposeItems(orderCreateRequestType.getCostCenterInputs()));
        }
        if (orderCreateRequestType.getCostCenterInfoNew() != null && CollectionUtil.isNotEmpty(
            orderCreateRequestType.getCostCenterInfoNew().getCostCenterInputs())) {
            approveObj.setCostCenters(
                buildCostCenterItemsNew(orderCreateRequestType.getCostCenterInfoNew().getCostCenterInputs()));
            approveObj.setProjectNames(
                buildProjectNoItemsNew(orderCreateRequestType.getCostCenterInfoNew().getCostCenterInputs()));
            approveObj.setTravelPurposeNames(
                buildTravelPurposeItemsNew(orderCreateRequestType.getCostCenterInfoNew().getCostCenterInputs()));
        }
        approveObj.setTravelers(
            getTravelers(orderCreateRequestType, checkAvailContextResponse, qconfigOfCertificateInitConfig));
        approveObj.setTravelerUids(getTravelersUids(orderCreateRequestType));
        approveObj.setTravelerInfoList(
            buildTravelerinfoList(orderCreateRequestType, checkAvailContextResponse, qconfigOfCertificateInitConfig));
        approveObj.setSubmitter(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        return approveObj;
    }

    /**
     * 转换关联项目
     */
    private static List<String> buildTravelPurposeItems(List<CostCenterInput> costCenterInputs) {
        if (CollectionUtil.isEmpty(costCenterInputs)) {
            return null;
        }
        List<String> projectNames = new ArrayList<>();
        costCenterInputs.stream().forEach(costCenterInput -> {
            if (StringUtil.isBlank(costCenterInput.getKey())) {
                return;
            }
            CostCenterKeyDto costCenterKeyDto = CostCenterKeyUtils.analysisCostCenterKey(costCenterInput.getKey());
            CostCenterTypeEnum costCenterTypeEnum =
                CostCenterTypeEnum.getEnumByCode(costCenterKeyDto.getCostCenterType());
            if (costCenterTypeEnum != CostCenterTypeEnum.TRIP_PURPOSE) {
                return;
            }
            projectNames.add(costCenterInput.getValue());
        });
        return projectNames;
    }

    /**
     * 转换关联项目
     */
    private static List<String> buildProjectNames(List<CostCenterInput> costCenterInputs) {
        if (CollectionUtil.isEmpty(costCenterInputs)) {
            return null;
        }
        List<String> projectNames = new ArrayList<>();
        costCenterInputs.stream().forEach(costCenterInput -> {
            if (StringUtil.isBlank(costCenterInput.getKey())) {
                return;
            }
            CostCenterKeyDto costCenterKeyDto = CostCenterKeyUtils.analysisCostCenterKey(costCenterInput.getKey());
            CostCenterTypeEnum costCenterTypeEnum =
                CostCenterTypeEnum.getEnumByCode(costCenterKeyDto.getCostCenterType());
            if (costCenterTypeEnum != CostCenterTypeEnum.PROJECT_NO) {
                return;
            }
            projectNames.add(costCenterInput.getValue());
        });
        return projectNames;
    }

    /**
     * 转换成本中心对象
     */
    private static List<CostCenter> buildCostCenter(List<CostCenterInput> costCenterInputs) {
        if (CollectionUtil.isEmpty(costCenterInputs)) {
            return null;
        }
        List<CostCenter> costCenters = new ArrayList<>();
        costCenterInputs.stream().forEach(costCenterInput -> {
            if (StringUtil.isBlank(costCenterInput.getKey()) || StringUtil.isBlank(costCenterInput.getValue())) {
                return;
            }
            CostCenterKeyDto costCenterKeyDto = CostCenterKeyUtils.analysisCostCenterKey(costCenterInput.getKey());
            CostCenterTypeEnum costCenterTypeEnum =
                CostCenterTypeEnum.getEnumByCode(costCenterKeyDto.getCostCenterType());
            if (Lists.newArrayList(CostCenterTypeEnum.PROJECT_NO, CostCenterTypeEnum.TRIP_PURPOSE)
                .contains(costCenterTypeEnum)) {
                return;
            }
            CostCenter costCenter = new CostCenter();
            costCenter.setCostCenterId(costCenterInput.getValue());
            costCenter.setCostCenterName(costCenterInput.getValue());
            if (Lists.newArrayList(CostCenterTypeEnum.DEFINE_1, CostCenterTypeEnum.DEFINE_2)
                .contains(costCenterTypeEnum)) {
                costCenter.setCostLevel(null);
            } else {
                costCenter.setCostLevel(costCenterTypeEnum.getLevel());
            }
            costCenters.add(costCenter);
        });
        return costCenters;
    }

    /**
     * 获取出行目的
     *
     * @param costCenterInputs
     * @return
     */
    protected List<String> buildTravelPurposeItemsNew(List<CostCenterInput> costCenterInputs) {
        List<String> travelPurposes = Lists.newArrayList();
        costCenterInputs.forEach(costCenterInput -> {
            if (costCenterInput == null) {
                return;
            }
            CostCenterKeyDto costCenterKeyDto = CostCenterKeyUtils.analysisCostCenterKey(costCenterInput.getItemKey());
            if (costCenterKeyDto == null || StringUtil.isBlank(costCenterKeyDto.getCostCenterType())) {
                return;
            }
            if (!CostCenterTypeEnum.TRIP_PURPOSE.getCode().equalsIgnoreCase(costCenterKeyDto.getCostCenterType())) {
                return;
            }
            travelPurposes.add(costCenterInput.getValue());
        });
        return travelPurposes;
    }

    /**
     * 获取项目号
     *
     * @return
     */
    protected List<String> buildProjectNoItemsNew(List<CostCenterInput> costCenterInputs) {
        List<String> projectNos = Lists.newArrayList();
        costCenterInputs.forEach(costCenterInput -> {
            if (costCenterInput == null) {
                return;
            }
            CostCenterKeyDto costCenterKeyDto = CostCenterKeyUtils.analysisCostCenterKey(costCenterInput.getItemKey());
            if (costCenterKeyDto == null || StringUtil.isBlank(costCenterKeyDto.getCostCenterType())) {
                return;
            }
            if (!CostCenterTypeEnum.PROJECT_NO.getCode().equalsIgnoreCase(costCenterKeyDto.getCostCenterType())) {
                return;
            }
            projectNos.add(costCenterInput.getValue());
        });
        return projectNos;
    }

    /**
     * 获取成本中心集合
     *
     * @param costCenterInputs
     * @return
     */
    protected List<CostCenter> buildCostCenterItemsNew(List<CostCenterInput> costCenterInputs) {
        List<CostCenter> costCenterTOS = Lists.newArrayList();
        costCenterInputs.forEach(costCenterInput -> {
            if (costCenterInput == null) {
                return;
            }
            CostCenterKeyDto costCenterKeyDto = CostCenterKeyUtils.analysisCostCenterKey(costCenterInput.getItemKey());
            if (costCenterKeyDto == null || StringUtil.isBlank(costCenterKeyDto.getCostCenterType())) {
                return;
            }
            if (!COST_CENTERS.contains(costCenterKeyDto.getCostCenterType())) {
                return;
            }
            costCenterTOS.add(buildCostCenterItemNew(costCenterInput.getValue(),
                CostCenterTypeEnum.getLevelByCode(costCenterKeyDto.getCostCenterType())));
        });
        return costCenterTOS.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取成本中心
     *
     * @param cost1
     * @param i
     * @return
     */
    private CostCenter buildCostCenterItemNew(String cost1, Integer i) {
        if (StringUtil.isNotBlank(cost1)) {
            final CostCenter costCenterTO = new CostCenter();
            costCenterTO.setCostCenterId(cost1);
            costCenterTO.setCostCenterName(cost1);
            costCenterTO.setCostLevel(i);
            return costCenterTO;
        }
        return null;
    }

    /**
     * 获取出行目的
     *
     * @param saveCostCenterInputVo
     * @return
     */
    protected List<String> getTravelPurposeItems(SaveCostCenterInputVo saveCostCenterInputVo) {
        List<String> travelPurposes = Lists.newArrayList();
        for (Map.Entry<String, SaveCostCenterInputItem> costItem : saveCostCenterInputVo.getItems().entrySet()) {
            // 出行目的
            if (StringUtil.isNotBlank(costItem.getValue().getTravelPurpose())) {
                travelPurposes.add(costItem.getValue().getTravelPurpose());
            }
        }
        return travelPurposes;
    }

    /**
     * 获取项目号
     *
     * @param saveCostCenterInputVo
     * @return
     */
    protected List<String> getProjectNoItems(SaveCostCenterInputVo saveCostCenterInputVo) {
        List<String> projectNos = Lists.newArrayList();
        for (Map.Entry<String, SaveCostCenterInputItem> costItem : saveCostCenterInputVo.getItems().entrySet()) {
            // 项目号
            if (StringUtil.isNotBlank(costItem.getValue().getProjectNo())) {
                projectNos.add(costItem.getValue().getProjectNo());
            }
        }
        return projectNos;
    }

    /**
     * 获取成本中心集合
     *
     * @param saveCostCenterInputVo
     * @return
     */
    protected List<CostCenter> getCostCenterItems(SaveCostCenterInputVo saveCostCenterInputVo) {
        List<CostCenter> costCenterTOS = Lists.newArrayList();
        for (Map.Entry<String, SaveCostCenterInputItem> costItem : saveCostCenterInputVo.getItems().entrySet()) {
            costCenterTOS.add(getCostCenterItem(costItem.getValue().getCost1(), 1));
            costCenterTOS.add(getCostCenterItem(costItem.getValue().getCost2(), 2));
            costCenterTOS.add(getCostCenterItem(costItem.getValue().getCost3(), 3));
            costCenterTOS.add(getCostCenterItem(costItem.getValue().getCost4(), 4));
            costCenterTOS.add(getCostCenterItem(costItem.getValue().getCost5(), 5));
            costCenterTOS.add(getCostCenterItem(costItem.getValue().getCost6(), 6));
        }
        return costCenterTOS.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取成本中心
     *
     * @param cost1
     * @param i
     * @return
     */
    private CostCenter getCostCenterItem(String cost1, Integer i) {
        if (StringUtil.isNotBlank(cost1)) {
            final CostCenter costCenterTO = new CostCenter();
            costCenterTO.setCostCenterId(cost1);
            costCenterTO.setCostCenterName(cost1);
            costCenterTO.setCostLevel(i);
            return costCenterTO;
        }
        return null;
    }

    /**
     * 构建出行人信息
     *
     * @param orderCreateRequestType
     * @param checkAvailContextResponse
     * @return
     */
    private List<TravelerInfo> buildTravelerinfoList(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailContextResponse,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {

        return Optional.ofNullable(orderCreateRequestType).map(OrderCreateRequestType::getHotelBookPassengerInputs)
            .orElse(Collections.emptyList()).stream().map(hotelPassengerInfo -> {
                TravelerInfo travelerInfo = new TravelerInfo();
                travelerInfo.setUid(
                    StringConstants.T.equalsIgnoreCase(hotelPassengerInfo.getHotelPassengerInput().getEmployee()) ?
                        hotelPassengerInfo.getHotelPassengerInput().getUid() : null);
                travelerInfo.setName(OrderCreateProcessorOfUtil.getUseName(hotelPassengerInfo,
                    OrderCreateProcessorOfUtil.getCityId(orderCreateRequestType), checkAvailContextResponse,
                    qconfigOfCertificateInitConfig));
                return travelerInfo;
            }).collect(Collectors.toList());
    }

    /**
     * 获取 uids
     *
     * @param orderCreateRequestType
     * @return
     */
    private List<String> getTravelersUids(OrderCreateRequestType orderCreateRequestType) {
        return Optional.ofNullable(orderCreateRequestType).map(OrderCreateRequestType::getHotelBookPassengerInputs)
            .orElse(Collections.emptyList()).stream()
            .filter(t -> StringConstants.T.equalsIgnoreCase(t.getHotelPassengerInput().getEmployee()))
            .map(HotelBookPassengerInput::getHotelPassengerInput).map(HotelPassengerInput::getUid)
            .collect(Collectors.toList());
    }

    /**
     * 获取名字
     *
     * @param orderCreateRequestType
     * @param checkAvailContextResponse
     * @return
     */
    private List<String> getTravelers(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailContextResponse,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {

        return Optional.ofNullable(orderCreateRequestType).map(OrderCreateRequestType::getHotelBookPassengerInputs)
            .orElse(Collections.emptyList()).stream()
            .filter(t -> StringConstants.T.equalsIgnoreCase(t.getHotelPassengerInput().getEmployee())).map(
                k -> OrderCreateProcessorOfUtil.getUseName(k,
                    OrderCreateProcessorOfUtil.getCityId(orderCreateRequestType), checkAvailContextResponse,
                    qconfigOfCertificateInitConfig)).collect(Collectors.toList());
    }

    /**
     * 构建预定信息
     *
     * @param orderCreateRequestType
     * @return
     */
    private CorpBookingInfo buildCorpBookingInfo(OrderCreateRequestType orderCreateRequestType,
        QueryCheckAvailContextResponseType checkAvailContextResponseType,
        GetTravelPolicyContextResponseType getTravelPolicyContextResponse,
        CheckTravelPolicyResponseType checkTravelPolicyResponse, ResourceToken resourceToken,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        WrapperOfAccount.AccountInfo accountInfo, Map<String, StrategyInfo> strategyInfoMap,
        GetCityBaseInfoResponseType getCityBaseInfoResponseType,
        QConfigOfAccountInfoConfig qConfigOfAccountInfoConfig) {
        CorpBookingInfo corpBookingInfo = new CorpBookingInfo();
        corpBookingInfo.setHotelOrderInfo(
            buildHotelOrderInfo(orderCreateRequestType, checkAvailContextResponseType, getTravelPolicyContextResponse,
                checkTravelPolicyResponse, resourceToken, checkAvailInfo, accountInfo, strategyInfoMap,
                getCityBaseInfoResponseType, qConfigOfAccountInfoConfig));
        return corpBookingInfo;
    }

    /**
     * 酒店信息
     *
     * @param orderCreateRequestType
     * @return
     */
    private HotelOrderInfo buildHotelOrderInfo(OrderCreateRequestType orderCreateRequestType,
        QueryCheckAvailContextResponseType checkAvailContextResponseType,
        GetTravelPolicyContextResponseType getTravelPolicyContextResponse,
        CheckTravelPolicyResponseType checkTravelPolicyResponse,
        ResourceToken resourceToken,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        WrapperOfAccount.AccountInfo accountInfo, Map<String, StrategyInfo> strategyInfoMap,
        GetCityBaseInfoResponseType getCityBaseInfoResponseType,
        QConfigOfAccountInfoConfig qConfigOfAccountInfoConfig) {

        // 酒店信息
        BookHotelInfoEntity hotelInfo = checkAvailContextResponseType.getHotelInfo();
        HotelOrderInfo hotelOrderInfo = new HotelOrderInfo();
        hotelOrderInfo.setTravelReason(isPublic(orderCreateRequestType));
        hotelOrderInfo.setBookingChannel(buildBookingChannel(orderCreateRequestType));
        hotelOrderInfo.setHighRisk(false);
        hotelOrderInfo.setHotelType(checkAvailInfo.getRoomType());
        hotelOrderInfo.setStarLevel(Null.or(hotelInfo.getStar(), String::valueOf));
        hotelOrderInfo.setProductType(
            buildProductType(checkAvailContextResponseType, orderCreateRequestType, strategyInfoMap, accountInfo,
                getCityBaseInfoResponseType, qConfigOfAccountInfoConfig));
        BookModeInfo bookModeInfo = orderCreateRequestType.getBookModeInfo();
        hotelOrderInfo.setBookType(getBookType(bookModeInfo.getBookingType()));
        // testnet,ctrip,98421---含公帐就匹配公帐的审批流
        boolean approvalMatchPayNew = QConfigOfCustomConfig.isSupport("approvalMatchPayNew",
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId());
        setHotelAmount(approvalMatchPayNew, checkTravelPolicyResponse, checkAvailInfo, hotelOrderInfo,
            accountInfo, orderCreateRequestType,resourceToken, getTravelPolicyContextResponse);
        hotelOrderInfo.setReachTravel(buildReachTravel(checkTravelPolicyResponse, orderCreateRequestType));
        hotelOrderInfo.setControl(buildControl(checkTravelPolicyResponse, orderCreateRequestType));
        hotelOrderInfo.setContingent(buildContingent(checkTravelPolicyResponse, orderCreateRequestType));
        hotelOrderInfo.setOverStandardStage(
            getOverStandardStage(checkTravelPolicyResponse, orderCreateRequestType, resourceToken, strategyInfoMap));
        hotelOrderInfo.setStandard(isStandardOld(
            Optional.ofNullable(getTravelPolicyContextResponse).map(GetTravelPolicyContextResponseType::getFinalPolicy)
                .orElse(null), hotelInfo, orderCreateRequestType, getTravelPolicyContextResponse, resourceToken,
            checkAvailInfo));
        hotelOrderInfo.setRcItemList(getRcItems(
            OrderCreateProcessorOfUtil.buildValidRcList(orderCreateRequestType.getRcInfos(),
                Optional.ofNullable(checkTravelPolicyResponse).map(CheckTravelPolicyResponseType::getCheckRcResult)
                    .orElse(null))));
        return hotelOrderInfo;
    }

    protected String buildProductType(QueryCheckAvailContextResponseType checkAvailContextResponseType,
        OrderCreateRequestType orderCreateRequestType, Map<String, StrategyInfo> strategyInfoMap,
        WrapperOfAccount.AccountInfo accountInfo, GetCityBaseInfoResponseType getCityBaseInfoResponseType,
        QConfigOfAccountInfoConfig qConfigOfAccountInfoConfig) {
        BookHotelInfoEntity hotelInfo = checkAvailContextResponseType.getHotelInfo();
        if (StrategyOfBookingInitUtil.cityWithinPointOfSale(strategyInfoMap)) {
            return accountInfo.cityWithinPointOfSale(
                buildCityBaseInfoEntity(getCityBaseInfoResponseType, orderCreateRequestType),
                qConfigOfAccountInfoConfig) ? DOMESTIC : OVERSEA;
        }
        return hotelInfo.isOversea() ? OVERSEA : DOMESTIC;
    }

    protected CityBaseInfoEntity buildCityBaseInfoEntity(GetCityBaseInfoResponseType getCityBaseInfoResponseType,
        OrderCreateRequestType orderCreateRequestType) {
        if (getCityBaseInfoResponseType == null || CollectionUtil.isEmpty(getCityBaseInfoResponseType.getCityBaseInfo())) {
            return null;
        }
        return getCityBaseInfoResponseType.getCityBaseInfo().stream().filter(
                cityBaseInfoEntity -> cityBaseInfoEntity != null && TemplateNumberUtil.isNotZeroAndNull(
                    cityBaseInfoEntity.getCityId()) && Objects.equals(cityBaseInfoEntity.getCityId(),
                    OrderCreateProcessorOfUtil.getCityId(orderCreateRequestType))).toList().stream().findFirst()
            .orElse(null);
    }

    private HotelOrderInfo setHotelAmount(boolean approvalMatchPayNew,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo, HotelOrderInfo hotelOrderInfo,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateRequestType orderCreateRequestType,
        ResourceToken resourceToken, GetTravelPolicyContextResponseType getTravelPolicyContextResponseType) {
        // 使用agg返回的金额
        boolean useNewAmount = useNewAmount(checkTravelPolicyResponseType);
        if (useNewAmount) {
            // 因公审批流金额及支付方式由agg计算
            setAmountUseNewAmount(approvalMatchPayNew, checkAvailInfo, hotelOrderInfo, accountInfo,
                orderCreateRequestType, resourceToken, checkTravelPolicyResponseType,
                getTravelPolicyContextResponseType);
            return hotelOrderInfo;
        }
        // 审批流优化逻辑----含公帐就匹配公帐的审批流 testnet,ctrip,98421
        if (approvalMatchPayNew) {
            setAmountApprovalMatchPayNew(checkAvailInfo, hotelOrderInfo, accountInfo, orderCreateRequestType,
                resourceToken);
            return hotelOrderInfo;
        }
        // 审批流匹配历史逻辑----仅公帐、混付、闪住支持按公帐匹配
        setAmountOld(checkAvailInfo, hotelOrderInfo, accountInfo, orderCreateRequestType, resourceToken,
            getTravelPolicyContextResponseType);
        return hotelOrderInfo;
    }

    private HotelOrderInfo setAmountUseNewAmount(boolean approvalMatchPayNew,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo, HotelOrderInfo hotelOrderInfo,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateRequestType orderCreateRequestType,
        ResourceToken resourceToken, CheckTravelPolicyResponseType checkTravelPolicyResponseType,
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType) {
        if (approvalMatchPayNew) {
            setAmountApprovalMatchPayNew(checkAvailInfo, hotelOrderInfo, accountInfo, orderCreateRequestType,
                resourceToken);
        } else {
            setAmountOld(checkAvailInfo, hotelOrderInfo, accountInfo, orderCreateRequestType, resourceToken,
                getTravelPolicyContextResponseType);
        }
        setAmountNew(hotelOrderInfo, checkTravelPolicyResponseType);
        return hotelOrderInfo;
    }

    protected boolean buildRoomPayCorpPay(HotelPayTypeEnum roomPayType, OrderCreateRequestType orderCreateRequestType,
        ResourceToken resourceToken) {
        if (roomPayType == null) {
            return false;
        }
        if (roomPayType.isCorpPay()) {
            return true;
        }
        if (roomPayType.isMixPay()) {
            return true;
        }
        if (roomPayType.isFlashPay()) {
            return true;
        }
        if (roomPayType == HotelPayTypeEnum.GUARANTEE_CORP_PAY) {
            return true;
        }
        if (roomPayType == HotelPayTypeEnum.PRBAL) {
            return true;
        }
        if (roomPayType == HotelPayTypeEnum.CORPORATE_CARD_PAY) {
            return true;
        }
        return MathUtils.isGreaterThanZero(buildCorpServiceAmount(orderCreateRequestType, resourceToken));
    }

    private HotelOrderInfo setAmountApprovalMatchPayNew(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        HotelOrderInfo hotelOrderInfo, WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken) {
        boolean useAggAmount = Arrays.asList(HotelBalanceTypeEnum.PP, HotelBalanceTypeEnum.USEFG)
            .contains(checkAvailInfo.getHotelBalanceTypeEnum());
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (roomPayType == null || resourceToken == null) {
            return hotelOrderInfo;
        }
        boolean roomPayCorpPay = buildRoomPayCorpPay(roomPayType, orderCreateRequestType, resourceToken);
        boolean onlyServiceFeeCorp =
            MathUtils.isGreaterThanZero(buildCorpServiceAmount(orderCreateRequestType, resourceToken)) && (
                roomPayType == HotelPayTypeEnum.CASH || roomPayType == HotelPayTypeEnum.GUARANTEE_SELF_PAY);
        hotelOrderInfo.setCurrencyType(accountInfo.settlementCurrencyForAgg());
        hotelOrderInfo.setPayType(roomPayCorpPay ? CORP : PERSONAL);
        // 预付房型使用agg费用计算返回的金额
        if (useAggAmount) {
            // 按公帐匹配审批流 房费公帐、混付、闪住支持按公帐匹配
            if (roomPayCorpPay) {
                if (roomPayType == HotelPayTypeEnum.PRBAL) {
                    BigDecimal orderAmount = Optional.ofNullable(resourceToken.getBookInitResourceToken())
                        .map(BookInitResourceToken::getAggBookPriceResourceToken)
                        .map(AggBookPriceResourceToken::getVirtualPaymentAmount).orElse(BigDecimal.ZERO);
                    hotelOrderInfo.setOrderAmount(orderAmount.toPlainString());
                    hotelOrderInfo.setNightAmount(
                        buildNightAmountByOrderAmount(orderAmount, orderCreateRequestType).toPlainString());
                    return hotelOrderInfo;
                }
                if (roomPayType == HotelPayTypeEnum.CORPORATE_CARD_PAY) {
                    BigDecimal orderAmount = Optional.ofNullable(resourceToken.getBookInitResourceToken())
                        .map(BookInitResourceToken::getAggBookPriceResourceToken)
                        .map(AggBookPriceResourceToken::getCorporateCardPaymentAmount).orElse(BigDecimal.ZERO);
                    hotelOrderInfo.setOrderAmount(orderAmount.toPlainString());
                    hotelOrderInfo.setNightAmount(
                        buildNightAmountByOrderAmount(orderAmount, orderCreateRequestType).toPlainString());
                    return hotelOrderInfo;
                }
                BigDecimal orderAmount = Optional.ofNullable(resourceToken.getBookInitResourceToken())
                    .map(BookInitResourceToken::getAggBookPriceResourceToken)
                    .map(AggBookPriceResourceToken::getAccountPaymentAmount).orElse(BigDecimal.ZERO);
                hotelOrderInfo.setOrderAmount(orderAmount.toPlainString());
                hotelOrderInfo.setNightAmount(
                    buildNightAmountByOrderAmount(orderAmount, orderCreateRequestType).toPlainString());
                return hotelOrderInfo;
            }
            // 按个人匹配审批流
            BigDecimal orderAmount = Optional.ofNullable(resourceToken.getBookInitResourceToken())
                .map(BookInitResourceToken::getAggBookPriceResourceToken)
                .map(AggBookPriceResourceToken::getIndividualPaymentAmount).orElse(BigDecimal.ZERO);
            hotelOrderInfo.setOrderAmount(orderAmount.toPlainString());
            hotelOrderInfo.setNightAmount(
                buildNightAmountByOrderAmount(orderAmount, orderCreateRequestType).toPlainString());
            return hotelOrderInfo;
        }
        // 现付房型使用agg可定金额BFF做逻辑计算
        if (onlyServiceFeeCorp) {
            // 按服务费公帐匹配审批流 服务费公帐+房费个人担保/现付
            BigDecimal orderAmount = buildCorpServiceAmount(orderCreateRequestType, resourceToken);
            hotelOrderInfo.setOrderAmount(orderAmount.toString());
            hotelOrderInfo.setNightAmount(
                buildNightAmountByOrderAmount(orderAmount, orderCreateRequestType).toPlainString());
            return hotelOrderInfo;
        }
        // 按公帐匹配审批流 服务费个人+房费公帐担保
        if (roomPayType == HotelPayTypeEnum.GUARANTEE_CORP_PAY) {
            BigDecimal orderAmount = buildOrderAmount(checkAvailInfo, orderCreateRequestType).add(
                buildCorpServiceAmount(orderCreateRequestType, resourceToken));
            hotelOrderInfo.setOrderAmount(orderAmount.toPlainString());
            hotelOrderInfo.setNightAmount(
                buildNightAmountByOrderAmount(orderAmount, orderCreateRequestType).toPlainString());
            return hotelOrderInfo;
        }
        // 按个人匹配审批流 服务费个人+房费个人担保/现付
        BigDecimal orderAmount = buildOrderAmount(checkAvailInfo, orderCreateRequestType).add(
            buildSelfServiceAmount(orderCreateRequestType, resourceToken));
        hotelOrderInfo.setOrderAmount(orderAmount.toPlainString());
        hotelOrderInfo.setNightAmount(
            buildNightAmountByOrderAmount(orderAmount, orderCreateRequestType).toPlainString());
        return hotelOrderInfo;
    }

    private BigDecimal buildNightAmountByOrderAmount(BigDecimal orderAmount,
        OrderCreateRequestType orderCreateRequestType) {
        Integer roomQuantity = orderCreateRequestType.getHotelBookInput().getRoomQuantity();
        int days = HotelDateRangeUtil.getDays(orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo());
        if (null != roomQuantity && 0 != days) {
            return orderAmount.divide(new BigDecimal(roomQuantity).multiply(new BigDecimal(days)), 2,
                BigDecimal.ROUND_HALF_UP);
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal buildCorpServiceAmount(OrderCreateRequestType orderCreateRequestType,
        ResourceToken resourceToken) {
        if (HotelPayTypeUtil.getServicePayType(orderCreateRequestType.getHotelPayTypeInput(), resourceToken)
            == HotelPayTypeEnum.CORP_PAY) {
            return Optional.ofNullable(resourceToken.getBookInitResourceToken())
                .map(BookInitResourceToken::getServiceChargeResourceToken)
                .map(ServiceChargeResourceToken::getServiceChargeAmount).orElse(BigDecimal.ZERO);
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal buildSelfServiceAmount(OrderCreateRequestType orderCreateRequestType,
        ResourceToken resourceToken) {
        if (HotelPayTypeUtil.getServicePayType(orderCreateRequestType.getHotelPayTypeInput(), resourceToken)
            == HotelPayTypeEnum.SELF_PAY) {
            return Optional.ofNullable(resourceToken.getBookInitResourceToken())
                .map(BookInitResourceToken::getServiceChargeResourceToken)
                .map(ServiceChargeResourceToken::getServiceChargeAmount).orElse(BigDecimal.ZERO);
        }
        return BigDecimal.ZERO;
    }

    /**
     * 审批金额是否加服务费
     * 房费个人现付/预付个人+服务费个人
     * 房费公帐/闪住/混付+服务费公帐
     * 差标包含服务费开关开启
     */
    private boolean buildIncludeServiceFee(HotelPayTypeEnum roomPayTypeEnum, HotelPayTypeEnum servicePayTypeEnum,
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType) {
        if (roomPayTypeEnum == null) {
            return false;
        }
        if (roomPayTypeEnum.isCorpPay() || roomPayTypeEnum.isMixPay() || roomPayTypeEnum.isFlashPay()) {
            if (servicePayTypeEnum != null && servicePayTypeEnum.isCorpPay()) {
                return true;
            }
            BooleanUtil.isTrue(Optional.ofNullable(getTravelPolicyContextResponseType)
                .map(GetTravelPolicyContextResponseType::getAccountSetting)
                .map(AccountSettingType::isIncludeServiceCharge).orElse(false));
        }
        if (servicePayTypeEnum != null && servicePayTypeEnum.isSelfPay()) {
            return true;
        }
        return BooleanUtil.isTrue(Optional.ofNullable(getTravelPolicyContextResponseType)
            .map(GetTravelPolicyContextResponseType::getAccountSetting).map(AccountSettingType::isIncludeServiceCharge)
            .orElse(false));
    }


    private HotelOrderInfo setAmountOld(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        HotelOrderInfo hotelOrderInfo, WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken,
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType) {
        HotelPayTypeEnum roomPayTypeEnum =
            HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        HotelPayTypeEnum servicePayTypeEnum =
            HotelPayTypeUtil.getServicePayType(orderCreateRequestType.getHotelPayTypeInput(), resourceToken);
        BigDecimal serviceFee = Optional.ofNullable(resourceToken.getBookInitResourceToken())
            .map(BookInitResourceToken::getServiceChargeResourceToken)
            .map(ServiceChargeResourceToken::getServiceChargeAmount).orElse(BigDecimal.ZERO);
        BigDecimal orderAmount = buildOrderAmount(checkAvailInfo, orderCreateRequestType);
        if (buildIncludeServiceFee(roomPayTypeEnum, servicePayTypeEnum, getTravelPolicyContextResponseType)) {
            hotelOrderInfo.setOrderAmount(orderAmount.add(serviceFee).toPlainString());
        } else {
            hotelOrderInfo.setOrderAmount(orderAmount.toPlainString());
        }
        hotelOrderInfo.setCurrencyType(accountInfo.settlementCurrencyForAgg());
        HotelPayTypeEnum hotelPayTypeEnum =
            HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        hotelOrderInfo.setPayType(matchFlowByCorp(hotelPayTypeEnum) ? CORP : PERSONAL);
        if (BooleanUtil.isTrue(Optional.ofNullable(getTravelPolicyContextResponseType)
            .map(GetTravelPolicyContextResponseType::getAccountSetting).map(AccountSettingType::isIncludeServiceCharge)
            .orElse(false))) {
            BigDecimal serviceFeeNight = Optional.ofNullable(resourceToken.getBookInitResourceToken())
                .map(BookInitResourceToken::getServiceChargeResourceToken)
                .map(ServiceChargeResourceToken::getServiceFeeByRoomNight).orElse(BigDecimal.ZERO);
            hotelOrderInfo.setNightAmount(
                buildNightAmount(checkAvailInfo, orderCreateRequestType).add(serviceFeeNight).toPlainString());
        } else {
            hotelOrderInfo.setNightAmount(buildNightAmount(checkAvailInfo, orderCreateRequestType).toPlainString());
        }
        return hotelOrderInfo;
    }

    private boolean matchFlowByCorp(HotelPayTypeEnum roomHotelPayTypeEnum) {
        if (roomHotelPayTypeEnum == null) {
            return false;
        }
        if (roomHotelPayTypeEnum.isCorpPay()) {
            return true;
        }
        if (roomHotelPayTypeEnum.isMixPay()) {
            return true;
        }
        if (roomHotelPayTypeEnum.isFlashPay()) {
            return true;
        }
        if (roomHotelPayTypeEnum == HotelPayTypeEnum.PRBAL) {
            return true;
        }
        if (roomHotelPayTypeEnum == HotelPayTypeEnum.CORPORATE_CARD_PAY) {
            return true;
        }
        return false;
    }

    private BigDecimal buildNightAmount(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        OrderCreateRequestType orderCreateRequestType) {
        BigDecimal orderAmount = checkAvailInfo.getRoomAmount().subtract(buildCouponAmount(orderCreateRequestType));
        Integer roomQuantity = orderCreateRequestType.getHotelBookInput().getRoomQuantity();
        int days = HotelDateRangeUtil.getDays(orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo());
        if (null != roomQuantity && 0 != days) {
            return orderAmount.divide(new BigDecimal(roomQuantity).multiply(new BigDecimal(days)), 2,
                BigDecimal.ROUND_HALF_UP);
        }
        return BigDecimal.ZERO;
    }

    private BigDecimal buildOrderAmount(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        OrderCreateRequestType orderCreateRequestType) {
        BigDecimal couponAmount = buildCouponAmount(orderCreateRequestType);
        return checkAvailInfo.getRoomAmount().subtract(couponAmount);
    }

    private static BigDecimal buildCouponAmount(OrderCreateRequestType orderCreateRequestType) {
        List<CouponToken> couponTokenList = buildCouponTokenList(orderCreateRequestType);
        if (CollectionUtils.isNotEmpty(couponTokenList)) {
            return couponTokenList.stream().map(CouponToken::getCouponAmount).filter(Objects::nonNull)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        }
        return BigDecimal.ZERO;
    }

    private static List<CouponToken> buildCouponTokenList(OrderCreateRequestType orderCreateRequestType) {
        if (orderCreateRequestType.getCouponInfoInput() == null || CollectionUtils.isEmpty(
            orderCreateRequestType.getCouponInfoInput().getCouponDetailInputList())) {
            return null;
        }
        return orderCreateRequestType.getCouponInfoInput().getCouponDetailInputList().stream()
            .map(couponDetailInput -> {
                if (couponDetailInput == null) {
                    return null;
                }
                if (StringUtil.isBlank(couponDetailInput.getCouponToken())) {
                    return null;
                }
                return TokenParseUtil.parseToken(couponDetailInput.getCouponToken(), CouponToken.class);
            }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    protected Boolean buildControl(CheckTravelPolicyResponseType checkTravelPolicyResponseType,
        OrderCreateRequestType orderCreateRequestType) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())
            || checkTravelPolicyResponseType == null) {
            return false;
        }
        if (CommonConstant.CONTROL_BY_PSG.equalsIgnoreCase(
            checkTravelPolicyResponseType.getApprovalBillControlDimension())) {
            String verifyResult = Optional.ofNullable(checkTravelPolicyResponseType.getVerifyPassengerResult())
                .map(VerifyPassengerResultType::getVerifyResult).orElse(null);
            if (CommonConstant.PASS.equals(verifyResult) || CommonConstant.FAIL.equals(verifyResult)) {
                return true;
            }
            return false;
        }
        if (useNewAmount(checkTravelPolicyResponseType)) {
            String verifyResult = Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
                .map(VerifyApprovalBillResultType::getVerifyResult).orElse(null);
            if (CommonConstant.PASS.equals(verifyResult) || CommonConstant.FAIL.equals(verifyResult)) {
                return true;
            }
            return false;
        }
        return checkTravelPolicyResponseType.getVerifyApprovalBillResult() != null;
    }

    protected Boolean buildContingent(CheckTravelPolicyResponseType checkTravelPolicyResponseType,
        OrderCreateRequestType orderCreateRequestType) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())
            || checkTravelPolicyResponseType == null) {
            return false;
        }
        if (CommonConstant.CONTROL_BY_PSG.equalsIgnoreCase(
            checkTravelPolicyResponseType.getApprovalBillControlDimension())) {
            return CommonConstant.EMERGENCY.equalsIgnoreCase(
                Optional.ofNullable(checkTravelPolicyResponseType.getVerifyPassengerResult())
                    .map(VerifyPassengerResultType::getVerifyResult).orElse(null));
        }
        if (useNewAmount(checkTravelPolicyResponseType)) {
            return CommonConstant.EMERGENCY.equalsIgnoreCase(
                Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
                    .map(VerifyApprovalBillResultType::getVerifyResult).orElse(null));
        }
        return BooleanUtil.parseStr(true).equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getApprovalInput()).map(ApprovalInput::getEmergency)
                .orElse(null));
    }

    protected Boolean buildReachTravel(CheckTravelPolicyResponseType checkTravelPolicyResponseType,
        OrderCreateRequestType orderCreateRequestType) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())
            || checkTravelPolicyResponseType == null) {
            return null;
        }
        if (CommonConstant.CONTROL_BY_PSG.equalsIgnoreCase(
            checkTravelPolicyResponseType.getApprovalBillControlDimension())) {
            String verifyResult = Optional.ofNullable(checkTravelPolicyResponseType.getVerifyPassengerResult())
                .map(VerifyPassengerResultType::getVerifyResult).orElse(null);
            if (CommonConstant.PASS.equals(verifyResult)) {
                return true;
            }
            if (CommonConstant.FAIL.equals(verifyResult)) {
                return false;
            }
            return null;
        }
        if (useNewAmount(checkTravelPolicyResponseType)) {
            String verifyResult = Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
                .map(VerifyApprovalBillResultType::getVerifyResult).orElse(null);
            if (CommonConstant.PASS.equals(verifyResult)) {
                return true;
            }
            if (CommonConstant.FAIL.equals(verifyResult)) {
                return false;
            }
            return null;
        }
        if (checkTravelPolicyResponseType.getVerifyApprovalBillResult() == null) {
            return null;
        }
        if (BooleanUtils.isTrue(checkTravelPolicyResponseType.getVerifyApprovalBillResult().isInControl())) {
            return true;
        }
        return false;
    }

    protected HotelOrderInfo setAmountOld(HotelOrderInfo hotelOrderInfo, OrderCreateRequestType orderCreateRequestType,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType, ResourceToken resourceToken) {
        if (checkTravelPolicyResponseType.getControlledAmountDetailInfo() == null) {
            return hotelOrderInfo;
        }
        boolean approvalMatchPayNew = QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.APPROVAL_MATCH_PAY_NEW,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        HotelPayTypeEnum bffPayTypeEnum =
            HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        HotelPayTypeEnum servicePayTypeEnum =
            HotelPayTypeUtil.getServicePayType(orderCreateRequestType.getHotelPayTypeInput(), resourceToken);
        if (roomPayCorpPay(bffPayTypeEnum) || (servicePayTypeEnum == HotelPayTypeEnum.CORP_PAY
            && approvalMatchPayNew)) {
            if (MathUtils.isGreaterThanZero(Optional.ofNullable(
                    checkTravelPolicyResponseType.getControlledAmountDetailInfo().getAccountPaymentAmount())
                .map(PriceType::getPrice).orElse(BigDecimal.ZERO))) {
                return hotelOrderInfo;
            }
            hotelOrderInfo.setPayType(MATCH_APPROVAL_FLOW_PAY_TYPE_CORP);
            hotelOrderInfo.setCurrencyType(
                checkTravelPolicyResponseType.getControlledAmountDetailInfo().getAccountPaymentAmount().getCurrency());
            hotelOrderInfo.setOrderAmount(String.valueOf(
                checkTravelPolicyResponseType.getControlledAmountDetailInfo().getAccountPaymentAmount().getPrice()));
        } else {
            if (MathUtils.isGreaterThanZero(Optional.ofNullable(
                    checkTravelPolicyResponseType.getControlledAmountDetailInfo().getTotalPaymentAmount())
                .map(PriceType::getPrice).orElse(BigDecimal.ZERO))) {
                return hotelOrderInfo;
            }
            hotelOrderInfo.setPayType(MATCH_APPROVAL_FLOW_PAY_TYPE_PERSONAL);
            hotelOrderInfo.setCurrencyType(
                checkTravelPolicyResponseType.getControlledAmountDetailInfo().getTotalPaymentAmount().getCurrency());
            hotelOrderInfo.setOrderAmount(String.valueOf(
                checkTravelPolicyResponseType.getControlledAmountDetailInfo().getTotalPaymentAmount().getPrice()));
        }
        hotelOrderInfo.setNightAmount(String.valueOf(
            Optional.ofNullable(checkTravelPolicyResponseType.getControlledAmountDetailInfo().getAvgPaymentAmount())
                .map(PriceType::getPrice).orElse(BigDecimal.ZERO)));
        return hotelOrderInfo;
    }

    protected boolean roomPayCorpPay(HotelPayTypeEnum rooBffPayTypeEnum) {
        if (rooBffPayTypeEnum == null) {
            return false;
        }
        if (rooBffPayTypeEnum.isCorpPay()) {
            return true;
        }
        if (rooBffPayTypeEnum.isMixPay()) {
            return true;
        }
        if (rooBffPayTypeEnum.isFlashPay()) {
            return true;
        }
        if (rooBffPayTypeEnum == HotelPayTypeEnum.GUARANTEE_CORP_PAY) {
            return true;
        }
        if (rooBffPayTypeEnum == HotelPayTypeEnum.PRBAL) {
            return true;
        }
        if (rooBffPayTypeEnum == HotelPayTypeEnum.CORPORATE_CARD_PAY) {
            return true;
        }
        return false;
    }

    /**
     * 使用新金额逻辑 代表agg恒走深圳管控了
     *
     * @param checkTravelPolicyResponseType
     * @return
     */
    public boolean useNewAmount(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        return StringUtil.equalsIgnoreCase(Optional.ofNullable(checkTravelPolicyResponseType)
            .map(CheckTravelPolicyResponseType::getApprovalPriceOptimized).orElse(null), "T");
    }

    protected HotelOrderInfo setAmountNew(HotelOrderInfo hotelOrderInfo,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        if (MathUtils.isGreaterThanZero(
            Optional.ofNullable(checkTravelPolicyResponseType.getControlledAmountDetailInfo().getTotalPaymentAmount())
                .map(PriceType::getPrice).orElse(null))) {
            hotelOrderInfo.setTotalPay(getOrderAmountItem(
                checkTravelPolicyResponseType.getControlledAmountDetailInfo().getTotalPaymentAmount()));
        }
        if (MathUtils.isGreaterThanZero(
            Optional.ofNullable(checkTravelPolicyResponseType.getControlledAmountDetailInfo().getAccountPaymentAmount())
                .map(PriceType::getPrice).orElse(null))) {
            hotelOrderInfo.setAccountPay(getOrderAmountItem(
                checkTravelPolicyResponseType.getControlledAmountDetailInfo().getAccountPaymentAmount()));
        }
        if (MathUtils.isGreaterThanZero(Optional.ofNullable(
                checkTravelPolicyResponseType.getControlledAmountDetailInfo().getIndividualPaymentAmount())
            .map(PriceType::getPrice).orElse(null))) {
            hotelOrderInfo.setPersonPay(getOrderAmountItem(
                checkTravelPolicyResponseType.getControlledAmountDetailInfo().getIndividualPaymentAmount()));
        }
        if (MathUtils.isGreaterThanZero(
            Optional.ofNullable(checkTravelPolicyResponseType.getControlledAmountDetailInfo().getAvgPaymentAmount())
                .map(PriceType::getPrice).orElse(null))) {
            hotelOrderInfo.setNightPrice(getOrderAmountItem(
                checkTravelPolicyResponseType.getControlledAmountDetailInfo().getAvgPaymentAmount()));
        }
        hotelOrderInfo.setPayType(getPayType(checkTravelPolicyResponseType.getApprovalPaymentType()));
        return hotelOrderInfo;
    }

    protected OrderAmountItem getOrderAmountItem(PriceType priceType) {
        OrderAmountItem orderAmountItem = new OrderAmountItem();
        orderAmountItem.setAmount(priceType.getPrice().toPlainString());
        orderAmountItem.setCurrency(priceType.getCurrency());
        return orderAmountItem;
    }

    private static final String MATCH_APPROVAL_FLOW_PAY_TYPE_CORP = "C";
    private static final String MATCH_APPROVAL_FLOW_PAY_TYPE_PERSONAL = "P";
    private static final String MATCH_APPROVAL_FLOW_PAY_TYPE_MIX = "M";

    protected String getPayType(String approvalPaymentType) {
        if (StringUtil.isBlank(approvalPaymentType)) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.APPROVAL_PAYMENT_TYPE_ERROR,
                "approvalPaymentType error");
        }
        switch (approvalPaymentType) {
            case CORP_PAY:
            case BookingInitUtil.PRBAL:
            case BookingInitUtil.CORPORATE_CARD_PAY:
                return MATCH_APPROVAL_FLOW_PAY_TYPE_CORP;
            case PERSONAL_PAY:
                return MATCH_APPROVAL_FLOW_PAY_TYPE_PERSONAL;
            case MIX_PAY:
                return MATCH_APPROVAL_FLOW_PAY_TYPE_MIX;
            default:
                throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.APPROVAL_PAYMENT_TYPE_ERROR,
                    "approvalPaymentType error");
        }
    }

    /**
     * 获取校验超标RC的结果
     *
     * @param checkTravelPolicyResponse
     * @return
     */
    private String getOverStandardStage(CheckTravelPolicyResponseType checkTravelPolicyResponse,
        OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken,
        Map<String, StrategyInfo> strategyInfoMap) {
        CheckOverStandardRcInfoType checkOverStandardRcInfoType =
            OrderCreateProcessorOfUtil.getCheckOverStandardRcInfoType(checkTravelPolicyResponse,
                orderCreateRequestType.getHotelPayTypeInput(), resourceToken);
        if (StrategyOfBookingInitUtil.blockMatchOveStandardStageApprovalFlow(strategyInfoMap)) {
            return null;
        }
        if (StringUtilsExt.isBlank(
            Optional.ofNullable(checkOverStandardRcInfoType).map(CheckOverStandardRcInfoType::getFloatPriceOverDetail)
                .map(FloatPriceOverDetailInfoType::getOverType).orElse(null))) {
            return null;
        }
        switch (checkOverStandardRcInfoType.getFloatPriceOverDetail().getOverType()) {
            case OVER_BASE_STANDARD_UPPER_LIMIT:
                return "S";
            case OVER_FINAL_STANDARD_UPPER_LIMIT:
                return "F";
            case BELOW_BASE_STANDARD_UPPER_LIMIT:
            default:
                return null;
        }
    }

    /**
     * @param validRcInputs 有效rc集合（通过agg管控结果已经剔除过无效的rc）
     * @return 审批流仅需要 低价/协议/提前预定/重复预订 四种rc
     */
    private List<RcItem> getRcItems(List<RCInput> validRcInputs) {
        if (CollectionUtils.isEmpty(validRcInputs)) {
            return Collections.emptyList();
        }
        List<RcItem> rcItems = Lists.newArrayList();
        validRcInputs.stream().forEach(rcInput -> {
            if (rcInput == null || StringUtil.isBlank(rcInput.getRcToken())) {
                return;
            }
            RcToken rcToken = TokenParseUtil.parseToken(rcInput.getRcToken(), RcToken.class);
            if (rcToken == null) {
                return;
            }
            RcTypeEnum rcTypeEnum = RcTypeEnum.getType(rcToken.getType());
            if (rcTypeEnum == null) {
                return;
            }
            if (!Arrays.asList(RcTypeEnum.LOW_PRICE, RcTypeEnum.AGREEMENT, RcTypeEnum.BOOK_AHEAD,
                RcTypeEnum.CONFLICT_BOOK).contains(rcTypeEnum)) {
                return;
            }
            RcItem rcItem = new RcItem();
            rcItem.setRc(rcToken.getCode());
            rcItem.setRcType(rcTypeEnum.getRcType());
            rcItems.add(rcItem);
        });
        return rcItems;
    }

    /**
     * rcCode
     *
     * @param rcContent
     * @return
     */
    private Integer getRcType(RCContent rcContent) {
        if (rcContent == null || StringUtil.isBlank(rcContent.getType())) {
            return null;
        }
        return RcTypeEnum.getRcTypeByName(rcContent.getType());
    }

    /**
     * @param finalPolicy
     * @param hotelInfo
     * @return
     */
    private Boolean isStandardOld(FinalPolicyType finalPolicy, BookHotelInfoEntity hotelInfo,
        OrderCreateRequestType orderCreateRequestType,
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType,
        ResourceToken resourceToken, WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
        HotelPayTypeEnum hotelPayTypeEnum =
            HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (hotelPayTypeEnum == HotelPayTypeEnum.MIX_PAY) {
            return true;
        }
        if (finalPolicy == null) {
            return false;
        }
        Integer star = hotelInfo.getStar();
        FinalTravelPolicyType finalTravelPolicy = finalPolicy.getFinalTravelPolicy();
        Integer maxStar = Optional.ofNullable(finalTravelPolicy).map(FinalTravelPolicyType::getMaxStar).orElse(null);
        Integer minStar = Optional.ofNullable(finalTravelPolicy).map(FinalTravelPolicyType::getMinStar).orElse(null);
        if (null == maxStar || null == minStar) {
            return false;
        }
        BigDecimal maxSettlementAmount =
            Optional.ofNullable(finalTravelPolicy).map(FinalTravelPolicyType::getMaxSettlementPrice).orElse(null);
        BigDecimal minSettlementAmount =
            Optional.ofNullable(finalTravelPolicy).map(FinalTravelPolicyType::getMinSettlementPrice).orElse(null);
        if (null == maxSettlementAmount || null == minSettlementAmount) {
            return false;
        }
        boolean matchMaxStar = star <= maxStar || NumberUtils.INTEGER_ZERO.equals(maxStar);
        boolean matchMinStar = star >= minStar;
        BigDecimal customNightAmount = null;
        if (BooleanUtil.isTrue(Optional.ofNullable(getTravelPolicyContextResponseType)
            .map(GetTravelPolicyContextResponseType::getAccountSetting).map(AccountSettingType::isIncludeServiceCharge)
            .orElse(false))) {
            BigDecimal serviceFeeNight = Optional.ofNullable(resourceToken.getBookInitResourceToken())
                .map(BookInitResourceToken::getServiceChargeResourceToken)
                .map(ServiceChargeResourceToken::getServiceFeeByRoomNight).orElse(BigDecimal.ZERO);
            customNightAmount = buildNightAmount(checkAvailInfo, orderCreateRequestType).add(serviceFeeNight);
        } else {
            customNightAmount = buildNightAmount(checkAvailInfo, orderCreateRequestType);
        }
        boolean matchMaxPrice = maxSettlementAmount.compareTo(customNightAmount) >= NumberUtils.INTEGER_ZERO
            || maxSettlementAmount.compareTo(BigDecimal.ZERO) == NumberUtils.INTEGER_ZERO;
        boolean matchMinPrice = minSettlementAmount.compareTo(customNightAmount) <= NumberUtils.INTEGER_ZERO;
        return matchMaxStar && matchMinStar && matchMaxPrice && matchMinPrice;
    }

    /**
     * 预定类型
     *
     * @param bookingType
     * @return
     */
    private String getBookType(String bookingType) {
        if (StringUtil.isBlank(bookingType)) {
            return null;
        }
        switch (bookingType) {
            case SHARE_ROOM:
                return "P";
            case OTHER_ORDER_ROOM:
                return "A";
            default:
                return null;
        }
    }


    protected static String buildBookingChannel(OrderCreateRequestType orderCreateRequestType) {
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() == SourceFrom.Offline) {
            return BOOKING_CHANNEL_OFFLINE;
        }
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() == SourceFrom.Online) {
            return BOOKING_CHANNEL_ONLINE;
        }
        return BOOKING_CHANNEL_APP;
    }

    /**
     * 获取渠道
     *
     * @param orderCreateRequestType
     * @return
     */
    private String getChannel(OrderCreateRequestType orderCreateRequestType) {
        return Optional.ofNullable(orderCreateRequestType).map(OrderCreateRequestType::getIntegrationSoaRequestType)
            .map(IntegrationSoaRequestType::getTransferInfo).orElse(Lists.newArrayList()).stream()
            .filter(mapString -> CHANNEL.equalsIgnoreCase(mapString.getKey())).findFirst().map(MapString::getValue)
            .orElse(StringUtil.EMPTY);
    }

    /**
     * 因公因私
     *
     * @param orderCreateRequestType
     * @return
     */
    private Integer isPublic(OrderCreateRequestType orderCreateRequestType) {
        CorpPayInfo corpPayInfo = orderCreateRequestType.getCorpPayInfo();
        if (corpPayInfo == null) {
            return null;
        }
        return CorpPayInfoUtil.isPublic(corpPayInfo) ? TRAVEL_REASON_PUBLIC : TRAVEL_REASON_PRIVATE;
    }

    /**
     * 获取政策id
     *
     * @param orderCreateRequestType
     * @return
     */
    private String getPolicyUserId(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo) {
        String policyUid = Optional.ofNullable(orderCreateRequestType).map(OrderCreateRequestType::getHotelPolicyInput)
            .map(HotelPolicyInput::getPolicyInput).map(PolicyInput::getPolicyUid).orElse(StringUtil.EMPTY);
        return accountInfo.getPolicyUidForOtherPolicy(policyUid,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
    }

    /**
     * 获取用户id
     *
     * @param integrationSoaRequestType
     * @return
     */
    private String getUserId(IntegrationSoaRequestType integrationSoaRequestType) {
        return Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getUserInfo)
            .map(UserInfo::getUserId).orElse(StringUtil.EMPTY);
    }

    /**
     * 获取公司id
     *
     * @param integrationSoaRequestType
     * @return
     */
    private String getCorpId(IntegrationSoaRequestType integrationSoaRequestType) {
        return Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getUserInfo)
            .map(UserInfo::getCorpId).orElse(StringUtil.EMPTY);
    }

}
