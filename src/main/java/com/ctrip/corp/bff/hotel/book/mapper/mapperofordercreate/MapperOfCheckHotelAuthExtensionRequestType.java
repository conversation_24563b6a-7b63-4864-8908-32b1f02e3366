package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ApprovalInfoResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowApprovalResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8;
import com.ctrip.corp.bff.hotel.book.common.util.HotelAuthExtensionUtil;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckHotelAuthExtension;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.soa._20184.CheckHotelAuthExtensionRequestType;
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataResponseType;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/6/23 18:31
 */
@Component
public class MapperOfCheckHotelAuthExtensionRequestType extends
        AbstractMapper<Tuple1<WrapperOfCheckHotelAuthExtension>, CheckHotelAuthExtensionRequestType> {

    @Override
    protected CheckHotelAuthExtensionRequestType convert(
            Tuple1<WrapperOfCheckHotelAuthExtension> tuple) {
        WrapperOfCheckHotelAuthExtension wrapperOfCheckHotelAuthExtension = tuple.getT1();
        WrapperOfAccount.AccountInfo accountInfo = wrapperOfCheckHotelAuthExtension.getAccountInfo();
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType = wrapperOfCheckHotelAuthExtension
                .getGetTravelPolicyContextResponseType();
        CheckTravelPolicyResponseType checkTravelPolicyResponseType = wrapperOfCheckHotelAuthExtension
                .getCheckTravelPolicyResponseType();
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = wrapperOfCheckHotelAuthExtension
                .getQueryCheckAvailContextResponseType();
        OrderCreateRequestType orderCreateRequestType = wrapperOfCheckHotelAuthExtension.getOrderCreateRequestType();
        ResourceToken resourceToken = wrapperOfCheckHotelAuthExtension.getResourceToken();
        WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo = wrapperOfCheckHotelAuthExtension
                .getCheckAvailInfo();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig = wrapperOfCheckHotelAuthExtension
                .getQconfigOfCertificateInitConfig();
        Map<String, StrategyInfo> strategyInfoMap = wrapperOfCheckHotelAuthExtension.getStrategyInfoMap();
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType =
                wrapperOfCheckHotelAuthExtension.getGetOrderFoundationDataResponseType();
        OrderCreateToken orderCreateToken = wrapperOfCheckHotelAuthExtension.getOrderCreateToken();
        CheckHotelAuthExtensionRequestType checkHotelAuthExtensionRequestType =
                new CheckHotelAuthExtensionRequestType();
        checkHotelAuthExtensionRequestType.setUid(
                orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        checkHotelAuthExtensionRequestType.setOrderMode(HotelAuthExtensionUtil.getOrderMode(accountInfo));
        checkHotelAuthExtensionRequestType.setAuthorizedOrderId(buildAuthorizedOrderId(orderCreateRequestType,
                strategyInfoMap, getOrderFoundationDataResponseType, orderCreateToken));
        checkHotelAuthExtensionRequestType.setPolicyUid(HotelAuthExtensionUtil.getPolicyUid(orderCreateRequestType));
        checkHotelAuthExtensionRequestType.setHotelProductInfo(
                HotelAuthExtensionUtil.buildHotelProductInfo(orderCreateRequestType, queryCheckAvailContextResponseType,
                        baseCheckAvailInfo));
        checkHotelAuthExtensionRequestType.setSettlementAmount(
                HotelAuthExtensionUtil.buildSettlementAmount(queryCheckAvailContextResponseType));
        checkHotelAuthExtensionRequestType.setTravelControlInfo(
                HotelAuthExtensionUtil.buildTravelControlInfo(getTravelPolicyContextResponseType,
                        checkTravelPolicyResponseType, orderCreateRequestType, resourceToken));
        checkHotelAuthExtensionRequestType.setClientList(
                HotelAuthExtensionUtil.buildClientInfos(orderCreateRequestType, baseCheckAvailInfo,
                        qconfigOfCertificateInitConfig));
        checkHotelAuthExtensionRequestType.setTripId(buildTripId(orderCreateRequestType, strategyInfoMap,
                getOrderFoundationDataResponseType));
        return checkHotelAuthExtensionRequestType;
    }

    @Override
    protected ParamCheckResult check(
            Tuple1<WrapperOfCheckHotelAuthExtension> tuple) {
        return null;
    }

    protected Long buildTripId(OrderCreateRequestType orderCreateRequestType, Map<String, StrategyInfo> strategyInfoMap,
                               GetOrderFoundationDataResponseType getOrderFoundationDataResponseType) {
        Long artificialReuseNoTripId = OrderCreateProcessorOfUtil.buildArtificialReuseNoTripId(orderCreateRequestType,
                getOrderFoundationDataResponseType);
        if (TemplateNumberUtil.isNotZeroAndNull(artificialReuseNoTripId)) {
            return artificialReuseNoTripId;
        }
        return null;
    }

    protected Long buildAuthorizedOrderId(OrderCreateRequestType orderCreateRequestType,
                                          Map<String, StrategyInfo> strategyInfoMap,
                                          GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
                                          OrderCreateToken orderCreateToken) {
        Long approvalReuseReBookOrderId =
                OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(orderCreateRequestType, strategyInfoMap);
        if (TemplateNumberUtil.isNotZeroAndNull(approvalReuseReBookOrderId)) {
            return approvalReuseReBookOrderId;
        }
        Long artificialReuseNoOrderId = OrderCreateProcessorOfUtil.buildArtificialReuseNoOrderId(orderCreateRequestType,
                getOrderFoundationDataResponseType);
        if (TemplateNumberUtil.isNotZeroAndNull(artificialReuseNoOrderId)) {
            return artificialReuseNoOrderId;
        }
        if (StringUtil.isNotBlank(
                Optional.ofNullable(orderCreateToken.getFollowApprovalResult()).map(FollowApprovalResult::getFollowOrderNo)
                        .orElse(null))) {
            return Long.parseLong(orderCreateToken.getFollowApprovalResult().getFollowOrderNo());
        }
        return Long.parseLong(orderCreateRequestType.getFollowApprovalInfoInput().getFollowOrderId());
    }
}
