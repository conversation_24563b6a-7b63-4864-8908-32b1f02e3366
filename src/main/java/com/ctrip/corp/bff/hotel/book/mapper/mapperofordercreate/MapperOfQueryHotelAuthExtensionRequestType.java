package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8;
import com.ctrip.corp.bff.hotel.book.common.util.HotelAuthExtensionUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.soa._20184.QueryHotelAuthExtensionRequestType;
import org.springframework.stereotype.Component;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/6/23 18:31
 */
@Component
public class MapperOfQueryHotelAuthExtensionRequestType extends
    AbstractMapper<Tuple8<AccountInfo, GetTravelPolicyContextResponseType, CheckTravelPolicyResponseType,
                        QueryCheckAvailContextResponseType, OrderCreateRequestType, ResourceToken, BaseCheckAvailInfo,
        QconfigOfCertificateInitConfig>, QueryHotelAuthExtensionRequestType> {

    @Override
    protected QueryHotelAuthExtensionRequestType convert(
        Tuple8<WrapperOfAccount.AccountInfo, GetTravelPolicyContextResponseType, CheckTravelPolicyResponseType,
            QueryCheckAvailContextResponseType, OrderCreateRequestType, ResourceToken, WrapperOfCheckAvail.BaseCheckAvailInfo,
            QconfigOfCertificateInitConfig> tuple) {
        WrapperOfAccount.AccountInfo accountInfo = tuple.getT1();
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType = tuple.getT2();
        CheckTravelPolicyResponseType checkTravelPolicyResponseType = tuple.getT3();
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = tuple.getT4();
        OrderCreateRequestType orderCreateRequestType = tuple.getT5();
        ResourceToken resourceToken = tuple.getT6();
        WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo = tuple.getT7();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig = tuple.getT8();
        QueryHotelAuthExtensionRequestType queryHotelAuthExtensionRequestType =
            new QueryHotelAuthExtensionRequestType();
        queryHotelAuthExtensionRequestType.setUid(
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        queryHotelAuthExtensionRequestType.setOrderMode(HotelAuthExtensionUtil.getOrderMode(accountInfo));
        queryHotelAuthExtensionRequestType.setPolicyUid(HotelAuthExtensionUtil.getPolicyUid(orderCreateRequestType));
        queryHotelAuthExtensionRequestType.setSettlementAmount(
            HotelAuthExtensionUtil.buildSettlementAmount(queryCheckAvailContextResponseType));
        queryHotelAuthExtensionRequestType.setHotelProductInfo(
            HotelAuthExtensionUtil.buildHotelProductInfo(orderCreateRequestType, queryCheckAvailContextResponseType,
                baseCheckAvailInfo));
        queryHotelAuthExtensionRequestType.setTravelControlInfo(
            HotelAuthExtensionUtil.buildTravelControlInfo(getTravelPolicyContextResponseType,
                checkTravelPolicyResponseType, orderCreateRequestType, resourceToken));
        queryHotelAuthExtensionRequestType.setClientList(
            HotelAuthExtensionUtil.buildClientInfos(orderCreateRequestType, baseCheckAvailInfo,
                qconfigOfCertificateInitConfig));
        return queryHotelAuthExtensionRequestType;
    }

    @Override
    protected ParamCheckResult check(
        Tuple8<WrapperOfAccount.AccountInfo, GetTravelPolicyContextResponseType, CheckTravelPolicyResponseType,
            QueryCheckAvailContextResponseType, OrderCreateRequestType, ResourceToken, WrapperOfCheckAvail.BaseCheckAvailInfo,
            QconfigOfCertificateInitConfig> tuple) {
        return null;
    }
}
