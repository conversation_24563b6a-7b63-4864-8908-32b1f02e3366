package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.corp.order.service.orderfoundationcentercostinfoservice.CostCenterItemType;
import com.corp.order.service.orderfoundationcentercostinfoservice.OrderCostCenterType;
import com.corp.order.service.orderfoundationcentercostinfoservice.PassengerType;
import com.corp.order.service.orderfoundationcentercostinfoservice.SaveOrderCostCenterRequestType;
import com.ctrip.corp.bff.framework.hotel.common.util.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.SaveCostCenterInputItem;
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.SaveCostCenterInputVo;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.ExceptionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.ObjectUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.exception.BookingCheckErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfSaveCostCenter;
import com.ctrip.corp.bff.hotel.book.contract.CostCenterInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.profile.contract.SSOBaseInfo;
import com.ctrip.corp.bff.profile.contract.SSOCostCenterInfo;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType;
import com.ctrip.corp.foundation.common.util.CalendarUtils;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.google.common.collect.Lists;
import corp.user.service.costcenterService.matchCostCenter.CostCenterConfigType;
import corp.user.service.costcenterService.matchCostCenter.CostCenterContentType;
import corp.user.service.costcenterService.matchCostCenter.CostCenterExtConfigType;
import corp.user.service.costcenterService.matchCostCenter.CostCenterExtItemType;
import corp.user.service.costcenterService.matchCostCenter.CostCenterInfoType;
import corp.user.service.costcenterService.matchCostCenter.MatchCostCenterResponseType;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/26 17:17
 */
@Component public class MapperOfSaveOrderCostCenterRequestType
    extends AbstractMapper<Tuple1<WrapperOfSaveCostCenter>, SaveOrderCostCenterRequestType> {

    private static final int BIZ_TYPE_C_HOTEL = 4;
    private static final int BIZ_TYPE_M_HOTEL = 64;

    // CostCenter1 CostCenter2 CostCenter3 CostCenter4 CostCenter5 CostCenter6 Project JourneyReason CostCenterCustom1 CostCenterCustom2
    public static final List<String> SSO_COST_CENTER_KEY =
        Arrays.asList("CostCenter1", "CostCenter2", "CostCenter3", "CostCenter4", "CostCenter5", "CostCenter6",
            "CostCenterCustom1", "CostCenterCustom2", "Project", "JourneyReason");

    @Override protected SaveOrderCostCenterRequestType convert(Tuple1<WrapperOfSaveCostCenter> tuple1) {
        WrapperOfSaveCostCenter wrapperOfSaveCostCenter = tuple1.getT1();
        OrderCreateRequestType orderCreateRequestType = wrapperOfSaveCostCenter.getOrderCreateRequestType();
        OrderCreateToken orderCreateToken = wrapperOfSaveCostCenter.getOrderCreateToken();
        CreateOrderResponseType createOrderResponseType = wrapperOfSaveCostCenter.getCreateOrderResponseType();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = wrapperOfSaveCostCenter.getCheckAvailInfo();
        WrapperOfAccount.AccountInfo accountInfo = wrapperOfSaveCostCenter.getAccountInfo();
        MatchCostCenterResponseType matchCostCenterResponseType =
            wrapperOfSaveCostCenter.getMatchCostCenterResponseType();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig =
            wrapperOfSaveCostCenter.getQconfigOfCertificateInitConfig();
        CostCenterInfoType costCenterInfoType =
            Optional.ofNullable(matchCostCenterResponseType).map(MatchCostCenterResponseType::getResult)
                .orElse(new CostCenterInfoType());
        ApprovalTextInfoResponseType approvalTextInfoResponseType =
            wrapperOfSaveCostCenter.getApprovalTextInfoResponseType();
        SSOInfoQueryResponseType ssoInfoQueryResponseType = wrapperOfSaveCostCenter.getSsoInfoQueryResponseType();
        ResourceToken resourceToken = wrapperOfSaveCostCenter.getResourceToken();
        return buildSaveOrderCostCenterRequest(orderCreateRequestType, accountInfo, checkAvailInfo, orderCreateToken,
            createOrderResponseType, costCenterInfoType, approvalTextInfoResponseType, ssoInfoQueryResponseType,
            resourceToken, qconfigOfCertificateInitConfig);
    }

    @Override protected ParamCheckResult check(Tuple1<WrapperOfSaveCostCenter> tuple1) {
        MatchCostCenterResponseType matchCostCenterResponseType = tuple1.getT1().getMatchCostCenterResponseType();
        if (!tuple1.getT1().getOrderCreateToken().isUseOrderCreate()) {
            return null;
        }
        if (matchCostCenterResponseType == null) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.MATCH_COST_CENTER_ERROR, "");
        }
        return null;
    }

    private String buildJourneyNo(ApprovalTextInfoResponseType approvalTextInfoResponseType,
        SaveCostCenterInputItem orderCostCenterInputItem, ApprovalInput approvalInput,
        OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo) {
        // 紧急预订场景
        String emergencyName = buildEmergencyName(approvalTextInfoResponseType, approvalInput, accountInfo,
            orderCreateRequestType);
        if (StringUtil.isNotBlank(emergencyName)) {
            return emergencyName;
        }
        // 成本中心中传入了关联行程号 提前审批不会有此单号，因为页面不允许输入
        if (org.apache.commons.lang3.StringUtils.isNotBlank(
            Optional.ofNullable(orderCostCenterInputItem).map(SaveCostCenterInputItem::getJourneyNo).orElse(null))) {
            return orderCostCenterInputItem.getJourneyNo();
        }
        // 成本中心未传入关联行程号 选择了提前审批/出差申请单号
        if (org.apache.commons.lang3.StringUtils.isNotBlank(
            Optional.ofNullable(approvalInput).map(ApprovalInput::getSubApprovalNo).orElse(null))) {
            return approvalInput.getSubApprovalNo();
        }
        return null;
    }

    private String buildApprovalNo(ApprovalTextInfoResponseType approvalTextInfoResponseType,
        ApprovalInput approvalInput, WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType) {
        // 紧急预订场景
        String emergencyName = buildEmergencyName(approvalTextInfoResponseType, approvalInput, accountInfo,
            orderCreateRequestType);
        if (StringUtil.isNotBlank(emergencyName)) {
            return emergencyName;
        }
        // 成本中心未传入关联行程号 选择了提前审批/出差申请单号
        if (org.apache.commons.lang3.StringUtils.isNotBlank(
            Optional.ofNullable(approvalInput).map(ApprovalInput::getSubApprovalNo).orElse(null))) {
            return approvalInput.getSubApprovalNo();
        }
        return null;
    }

    private String buildEmergencyName(ApprovalTextInfoResponseType approvalTextInfoResponseType,
        ApprovalInput approvalInput, WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType) {
        if (!accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
            orderCreateRequestType.getCorpPayInfo())) {
            return null;
        }
        if (BooleanUtil.parseStr(true)
            .equalsIgnoreCase(Optional.ofNullable(approvalInput).map(ApprovalInput::getEmergency).orElse(null))
            && StringUtil.isBlank(
            Optional.ofNullable(approvalInput).map(ApprovalInput::getSubApprovalNo).orElse(null))) {
            return Optional.ofNullable(approvalTextInfoResponseType).map(ApprovalTextInfoResponseType::getEmergencyName)
                .orElse(null);
        }
        return null;
    }

    private boolean buildSSOCostCenter(ResourceToken resourceToken, SSOInfoQueryResponseType ssoInfoQueryResponseType) {
        if (TemplateNumberUtil.isNotZeroAndNull(
            Optional.ofNullable(resourceToken.getOrderResourceToken()).map(OrderResourceToken::getOrderId)
                .orElse(0L))) {
            return false;
        }
        if (CollectionUtil.isEmpty(
            Optional.ofNullable(ssoInfoQueryResponseType).map(SSOInfoQueryResponseType::getSsoBaseInfo)
                .map(SSOBaseInfo::getCostCenterInfo).map(SSOCostCenterInfo::getCostCenterInfo).orElse(null))) {
            return false;
        }
        Map<String, String> costCenterInfo =
            ssoInfoQueryResponseType.getSsoBaseInfo().getCostCenterInfo().getCostCenterInfo();
        return SSO_COST_CENTER_KEY.stream().anyMatch(key -> StringUtil.isNotBlank(costCenterInfo.get(key)));
    }

    /**
     * 拼装保存成本中心Request
     *
     * @return
     */
    protected SaveOrderCostCenterRequestType buildSaveOrderCostCenterRequest(
        OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo, OrderCreateToken orderCreateToken,
        CreateOrderResponseType createOrderResponseType, CostCenterInfoType costCenterInfoType,
        ApprovalTextInfoResponseType approvalTextInfoResponseType, SSOInfoQueryResponseType ssoInfoQueryResponseType,
        ResourceToken resourceToken, QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
        // 传入的成本中心解析结果
        SaveCostCenterInputVo saveCostCenterInputVo = JsonUtil.fromJsonIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getCostCenterInfo()).map(CostCenterInfo::getCostCenterJsonString)
                .orElse(null), SaveCostCenterInputVo.class);
        SaveOrderCostCenterRequestType saveOrderCostCenterRequest = new SaveOrderCostCenterRequestType();
        saveOrderCostCenterRequest.setSaveList(Lists.newArrayList());
        OrderCostCenterType orderCostCenterType = new OrderCostCenterType();
        saveOrderCostCenterRequest.getSaveList().add(orderCostCenterType);
        // 保存常用成本中心，默认传True
        orderCostCenterType.setFrequentlyUsedCostCenter(1);
        orderCostCenterType.setBizType(buildBizType(checkAvailInfo));
        orderCostCenterType.setOrderID(
            OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType));
        orderCostCenterType.setUid(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        orderCostCenterType.setPid(accountInfo.getPolicyUidForOtherPolicy(
            Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
                .map(PolicyInput::getPolicyUid).orElse(null),
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId()));
        orderCostCenterType.setCorpId(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId());
        String eid = orderCreateRequestType.getIntegrationSoaRequestType().getEid();
        if (StringUtil.isBlank(eid)) {
            eid = orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId();
        }
        orderCostCenterType.setEid(eid);

        orderCostCenterType.setCostCenterDesc(costCenterInfoType.getCostCenterDescription());
        orderCostCenterType.setCostCenterDescEn(costCenterInfoType.getCostCenterDescriptionEn());
        // 按照订单走的成本中心list
        SaveCostCenterInputItem orderCostCenterInputItemOrder =
            Optional.ofNullable(saveCostCenterInputVo).map(SaveCostCenterInputVo::getItems).orElse(new HashMap<>())
                .get("fdefault");
        String journeyNo = buildJourneyNo(approvalTextInfoResponseType, orderCostCenterInputItemOrder,
            orderCreateRequestType.getApprovalInput(), orderCreateRequestType, accountInfo);
        if (StringUtil.isNotBlank(journeyNo)) {
            orderCostCenterType.setJounaryNo(journeyNo);
            Optional<CostCenterExtConfigType> extInfo =
                buildExtInfo(costCenterInfoType, CostCenterResourceEnum.HOTEL_TRAVEL_NUMBER.getExtName());
            orderCostCenterType.setJounaryNoTitle(
                buildExtItemTitle(extInfo, CostCenterResourceEnum.HOTEL_TRAVEL_NUMBER));
            orderCostCenterType.setJounaryNoTitleEn(
                buildExtItemTitleEn(extInfo, CostCenterResourceEnum.HOTEL_TRAVEL_NUMBER));
        }
        boolean isSSOCostCenter = buildSSOCostCenter(resourceToken, ssoInfoQueryResponseType);
        String approvalNo =
            buildApprovalNo(approvalTextInfoResponseType, orderCreateRequestType.getApprovalInput(), accountInfo,
                orderCreateRequestType);
        if (orderCostCenterInputItemOrder != null) {
            if (ObjectUtil.isNotNull(orderCostCenterInputItemOrder.getProjectNo())) {
                Optional<CostCenterExtConfigType> extInfo =
                    buildExtInfo(costCenterInfoType, CostCenterResourceEnum.PROJECT_NUMBER.getExtName());
                orderCostCenterType.setProjectTitle(buildExtItemTitle(extInfo, CostCenterResourceEnum.PROJECT_NUMBER));
                orderCostCenterType.setProjectTitleEn(
                    buildExtItemTitleEn(extInfo, CostCenterResourceEnum.PROJECT_NUMBER));
                orderCostCenterType.setProject(orderCostCenterInputItemOrder.getProjectNo());
                if (isSSOCostCenter || StringUtil.isNotBlank(approvalNo)) {
                    // 单点登录只要带入就可以保存
                } else {
                    CostCenterExtItemType costCenterExtItemPN =
                        buildCostCenterExtItem(costCenterInfoType, CostCenterResourceEnum.PROJECT_NUMBER,
                            orderCostCenterInputItemOrder.getProjectNo(), orderCreateRequestType);
                    if (ObjectUtil.isNull(costCenterExtItemPN) && StringUtils.isNotBlank(
                        orderCostCenterInputItemOrder.getProjectNo())) {
                    }
                    if (ObjectUtil.isNotNull(costCenterExtItemPN)) {
                        orderCostCenterType.setProjectId(costCenterExtItemPN.getCostCenterExtId());
                        orderCostCenterType.setProjectCode(costCenterExtItemPN.getCostCenterExtCode());
                    }
                }
            }
            if (ObjectUtil.isNotNull(orderCostCenterInputItemOrder.getTravelPurpose())) {
                Optional<CostCenterExtConfigType> extInfo =
                    buildExtInfo(costCenterInfoType, CostCenterResourceEnum.TRAVEL_PURPOSE.getExtName());
                orderCostCenterType.setJounaryReasonTitle(
                    buildExtItemTitle(extInfo, CostCenterResourceEnum.TRAVEL_PURPOSE));
                orderCostCenterType.setJounaryReasonTitleEn(
                    buildExtItemTitleEn(extInfo, CostCenterResourceEnum.TRAVEL_PURPOSE));
                orderCostCenterType.setJounaryReason(orderCostCenterInputItemOrder.getTravelPurpose());
                if (isSSOCostCenter || StringUtil.isNotBlank(approvalNo)) {
                } else {
                    CostCenterExtItemType costCenterExtItemTP =
                        buildCostCenterExtItem(costCenterInfoType, CostCenterResourceEnum.TRAVEL_PURPOSE,
                            orderCostCenterInputItemOrder.getTravelPurpose(), orderCreateRequestType);
                    if (ObjectUtil.isNull(costCenterExtItemTP) && StringUtils.isNotBlank(
                        orderCostCenterInputItemOrder.getTravelPurpose())) {
                    }
                    if (ObjectUtil.isNotNull(costCenterExtItemTP)) {
                        orderCostCenterType.setJounaryReasonId(costCenterExtItemTP.getCostCenterExtId());
                        orderCostCenterType.setJounaryReasonCode(costCenterExtItemTP.getCostCenterExtCode());
                    }
                }
            }
            if (ObjectUtil.isNotNull(orderCostCenterInputItemOrder.getD1())) {
                Optional<CostCenterExtConfigType> extInfo =
                    buildExtInfo(costCenterInfoType, CostCenterResourceEnum.COST_CENTER_DEFINE1.getExtName());
                orderCostCenterType.setDefineFlag1Title(
                    buildExtItemTitle(extInfo, CostCenterResourceEnum.COST_CENTER_DEFINE1));
                orderCostCenterType.setDefineFlag1TitleEn(
                    buildExtItemTitleEn(extInfo, CostCenterResourceEnum.COST_CENTER_DEFINE1));
                orderCostCenterType.setDefineFlag1(orderCostCenterInputItemOrder.getD1());
            }

            if (ObjectUtil.isNotNull(orderCostCenterInputItemOrder.getD2())) {
                Optional<CostCenterExtConfigType> extInfo =
                    buildExtInfo(costCenterInfoType, CostCenterResourceEnum.COST_CENTER_DEFINE2.getExtName());
                orderCostCenterType.setDefineFlag2Title(
                    buildExtItemTitle(extInfo, CostCenterResourceEnum.COST_CENTER_DEFINE2));
                orderCostCenterType.setDefineFlag2TitleEn(
                    buildExtItemTitleEn(extInfo, CostCenterResourceEnum.COST_CENTER_DEFINE2));
                orderCostCenterType.setDefineFlag2(orderCostCenterInputItemOrder.getD2());
            }
        }
        orderCostCenterType.setCreateTimeStr(
            CalendarUtils.format(Calendar.getInstance(), CalendarUtils.YYYY_MM_DD_HH_MM_SS));

        if (costCenterInfoType.getFrequentlyUsedCostCenter() != null) {
            orderCostCenterType.setFrequentlyUsedCostCenter(costCenterInfoType.getFrequentlyUsedCostCenter() & 0xFF);
        }

        if (buildNeedCostCenter(costCenterInfoType, saveCostCenterInputVo, journeyNo)) {
            if (costCenterInfoType.getCostCenterContentlist() == null) {
                costCenterInfoType.setCostCenterContentlist(Lists.newArrayList());
                CostCenterConfigType tmpCostCenterConfigTypeTO = new CostCenterConfigType();
                CostCenterContentType costCenterContentType = new CostCenterContentType();
                costCenterContentType.setCostCenterConfigs(Lists.newArrayList());
                costCenterContentType.getCostCenterConfigs().add(tmpCostCenterConfigTypeTO);
                costCenterInfoType.getCostCenterContentlist().add(costCenterContentType);
            }
            Map<String, SaveCostCenterInputItem> orderCostCenterInputItemPsg =
                Optional.ofNullable(saveCostCenterInputVo).map(SaveCostCenterInputVo::getItems).orElse(new HashMap<>())
                    .entrySet().stream().filter(entry -> !"fdefault".equals(entry.getKey()))
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            List<CostCenterConfigType> costCenterConfigTypes = costCenterInfoType.getCostCenterContentlist().stream()
                .flatMap(list -> (list == null || CollectionUtil.isEmpty(list.getCostCenterConfigs())) ?
                    Arrays.asList(new CostCenterConfigType()).stream() : list.getCostCenterConfigs().stream())
                .collect(Collectors.toList());
            orderCostCenterType.setOrderCostCenters(
                builderCostCenterItemType(costCenterConfigTypes, orderCostCenterInputItemOrder));
            orderCostCenterType.setPassengers(
                builderPassengers(costCenterConfigTypes, orderCostCenterInputItemPsg, checkAvailInfo,
                    orderCreateRequestType, qconfigOfCertificateInitConfig));
            // 对比降噪 单下没有成本中心 但是有关联行程号放个空集合
            if (CollectionUtil.isEmpty(orderCostCenterType.getOrderCostCenters()) && StringUtil.isNotBlank(journeyNo)) {
                orderCostCenterType.setOrderCostCenters(Lists.newArrayList());
            }
        }
        return saveOrderCostCenterRequest;
    }

    protected boolean buildNeedCostCenter(CostCenterInfoType costCenterInfoType,
        SaveCostCenterInputVo saveCostCenterInputVo, String journeyNo) {
        // ams配置了成本中心
        if (costCenterInfoType != null && CollectionUtil.isNotEmpty(costCenterInfoType.getCostCenterContentlist())) {
            return true;
        }
        if (StringUtil.isNotBlank(journeyNo)) {
            return true;
        }
        if (CollectionUtil.isEmpty(
            Optional.ofNullable(saveCostCenterInputVo).map(SaveCostCenterInputVo::getItems).orElse(new HashMap<>()))) {
            return false;
        }
        // 按照订单走的成本中心list
        SaveCostCenterInputItem orderCostCenterInputItemOrder =
            Optional.ofNullable(saveCostCenterInputVo).map(SaveCostCenterInputVo::getItems).orElse(new HashMap<>())
                .get("fdefault");
        if (orderCostCenterInputItemOrder != null && buildNeedCostCenterOrder(orderCostCenterInputItemOrder)) {
            return true;
        }
        // 按人走的成本中心
        Map<String, SaveCostCenterInputItem> orderCostCenterInputItemPsg =
            Optional.ofNullable(saveCostCenterInputVo).map(SaveCostCenterInputVo::getItems).orElse(new HashMap<>())
                .entrySet().stream().filter(entry -> !"fdefault".equals(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        if (CollectionUtil.isNotEmpty(orderCostCenterInputItemPsg)) {
            return true;
        }
        return false;
    }

    protected boolean buildNeedCostCenterOrder(SaveCostCenterInputItem orderCostCenterInputItemOrder) {
        if (orderCostCenterInputItemOrder == null) {
            return false;
        }
        if (Objects.nonNull(orderCostCenterInputItemOrder.getCost1())) {
            return true;
        }
        if (Objects.nonNull(orderCostCenterInputItemOrder.getCost2())) {
            return true;
        }
        if (Objects.nonNull(orderCostCenterInputItemOrder.getCost3())) {
            return true;
        }
        if (Objects.nonNull(orderCostCenterInputItemOrder.getCost4())) {
            return true;
        }
        if (Objects.nonNull(orderCostCenterInputItemOrder.getCost5())) {
            return true;
        }
        if (Objects.nonNull(orderCostCenterInputItemOrder.getCost6())) {
            return true;
        }
        if (Objects.nonNull(orderCostCenterInputItemOrder.getTravelPurpose())) {
            return true;
        }
        if (Objects.nonNull(orderCostCenterInputItemOrder.getProjectNo())) {
            return true;
        }
        if (Objects.nonNull(orderCostCenterInputItemOrder.getJourneyNo())) {
            return true;
        }
        if (Objects.nonNull(orderCostCenterInputItemOrder.getD1())) {
            return true;
        }
        if (Objects.nonNull(orderCostCenterInputItemOrder.getD2())) {
            return true;
        }
        return false;
    }

    /**
     * 拼接保存用的成本中心集合
     *
     * @param
     * @return
     */
    private List<CostCenterItemType> builderCostCenterItemType(List<CostCenterConfigType> costCenterConfigTypeTOS,
        SaveCostCenterInputItem saveCostCenterInputItem) {
        return builderCostCenterItemTypeByPassenger(costCenterConfigTypeTOS, saveCostCenterInputItem);
    }

    /**
     * 拼接保存用的成本中心集合
     *
     * @param
     * @return
     */
    private List<CostCenterItemType> builderCostCenterItemTypeByPassenger(
        List<CostCenterConfigType> costCenterConfigTypes, SaveCostCenterInputItem saveCostCenterInputItem) {
        if (saveCostCenterInputItem == null) {
            return null;
        }
        List<CostCenterItemType> costCenterItemTypeList = Lists.newArrayList();

        if (Objects.nonNull(saveCostCenterInputItem.getCost4())) {
            CostCenterItemType costItem4 =
                buildCostCenterItemType(CostCenterResourceEnum.COST_CENTER_CC_4, saveCostCenterInputItem.getCost4(),
                    costCenterConfigTypes);
            costCenterItemTypeList.add(costItem4);
        }

        if (Objects.nonNull(saveCostCenterInputItem.getCost3())) {
            CostCenterItemType costItem3 =
                buildCostCenterItemType(CostCenterResourceEnum.COST_CENTER_CC_3, saveCostCenterInputItem.getCost3(),
                    costCenterConfigTypes);
            costCenterItemTypeList.add(costItem3);
        }

        if (Objects.nonNull(saveCostCenterInputItem.getCost2())) {
            CostCenterItemType costItem2 =
                buildCostCenterItemType(CostCenterResourceEnum.COST_CENTER_CC_2, saveCostCenterInputItem.getCost2(),
                    costCenterConfigTypes);
            costCenterItemTypeList.add(costItem2);
        }
        if (Objects.nonNull(saveCostCenterInputItem.getCost1())) {
            CostCenterItemType costItem1 =
                buildCostCenterItemType(CostCenterResourceEnum.COST_CENTER_CC_1, saveCostCenterInputItem.getCost1(),
                    costCenterConfigTypes);
            costCenterItemTypeList.add(costItem1);
        }

        if (Objects.nonNull(saveCostCenterInputItem.getCost6())) {
            CostCenterItemType costItem6 =
                buildCostCenterItemType(CostCenterResourceEnum.COST_CENTER_CC_6, saveCostCenterInputItem.getCost6(),
                    costCenterConfigTypes);
            costCenterItemTypeList.add(costItem6);
        }
        if (Objects.nonNull(saveCostCenterInputItem.getCost5())) {
            CostCenterItemType costItem5 =
                buildCostCenterItemType(CostCenterResourceEnum.COST_CENTER_CC_5, saveCostCenterInputItem.getCost5(),
                    costCenterConfigTypes);
            costCenterItemTypeList.add(costItem5);
        }
        return CollectionUtil.isEmpty(costCenterItemTypeList) ? null : costCenterItemTypeList;
    }

    protected CostCenterItemType buildCostCenterItemType(CostCenterResourceEnum costCenterResourceEnum,
        String costCenterValue, List<CostCenterConfigType> costCenterConfigTypes) {
        CostCenterItemType costItem = new CostCenterItemType();
        costItem.setCostCenterKey(costCenterResourceEnum.getValue());
        costItem.setCostCenterValue(costCenterValue);
        // 原有代码也没有保存成本中心英文值的逻辑
        costItem.setCostCenterTitle(costCenterResourceEnum.buildDefaultTitle());
        costItem.setCostCenterTitleEn(costCenterResourceEnum.buildDefaultTitleEn());

        // 结合后台配置Title保存
        if (buildCostCenterConfigByConfig(costCenterConfigTypes, costCenterResourceEnum.getValue())) {
            Optional<CostCenterConfigType> costCenterConfigByConfigs =
                getCostCenterConfigByConfigs(costCenterConfigTypes, costCenterResourceEnum.getValue());
            if (costCenterConfigByConfigs.isPresent()) {
                if (StringUtil.isNotBlank(costCenterConfigByConfigs.get().getCostCenterTitle())) {
                    costItem.setCostCenterTitle(costCenterConfigByConfigs.get().getCostCenterTitle());
                }
                if (StringUtil.isNotBlank(costCenterConfigByConfigs.get().getCostCenterTitleEn())) {
                    costItem.setCostCenterTitleEn(costCenterConfigByConfigs.get().getCostCenterTitleEn());
                }
            }
        }
        return costItem;
    }

    public boolean buildCostCenterConfigByConfig(Collection<CostCenterConfigType> configs, Integer level) {
        if (level == null) {
            return false;
        }
        return getCostCenterConfigByConfigs(configs, level).isPresent();
    }

    public Optional<CostCenterConfigType> getCostCenterConfigByConfigs(Collection<CostCenterConfigType> configTos,
        Integer level) {
        if (level == null) {
            return Optional.empty();
        }
        if (configTos == null) {
            return Optional.empty();
        }
        for (CostCenterConfigType configTo : configTos) {
            if (configTo != null && configTo.getLevel() != null && configTo.getLevel().equals(level)) {
                return Optional.of(configTo);
            }
        }
        return Optional.empty();
    }

    /**
     * 拼装按人走的成本中心集合（不按人走的成本中心也需要保存人的信息）
     *
     * @param saveCostCenterInputItemMap
     * @return
     */
    private List<PassengerType> builderPassengers(List<CostCenterConfigType> costCenterConfigTypes,
        Map<String, SaveCostCenterInputItem> saveCostCenterInputItemMap,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo, OrderCreateRequestType orderCreateRequestType,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
        List<PassengerType> passengerTypeList = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(saveCostCenterInputItemMap)) {
            for (Map.Entry<String, SaveCostCenterInputItem> passengerEntrySet : saveCostCenterInputItemMap.entrySet()) {
                PassengerType passengerType = new PassengerType();
                passengerType.setPassengerId(passengerEntrySet.getKey());
                HotelBookPassengerInput passengerInfo =
                    buildHotelBookPassengerInputById(orderCreateRequestType.getHotelBookPassengerInputs(),
                        passengerEntrySet.getKey());
                if (ObjectUtil.isNotNull(passengerInfo)) {
                    passengerType.setPassengerName(OrderCreateProcessorOfUtil.getUseName(passengerInfo,
                        orderCreateRequestType.getCityInput().getCityId(), checkAvailInfo, qconfigOfCertificateInitConfig));
                    passengerType.setIsEmployee(
                        "T".equalsIgnoreCase(passengerInfo.getHotelPassengerInput().getEmployee()) ? 1 : 0);
                }
                passengerType.setCostCenters(
                    builderCostCenterItemType(costCenterConfigTypes, passengerEntrySet.getValue()));
                passengerTypeList.add(passengerType);
            }
        } else if (CollectionUtil.isNotEmpty(orderCreateRequestType.getHotelBookPassengerInputs())) {
            for (HotelBookPassengerInput passenger : orderCreateRequestType.getHotelBookPassengerInputs()) {
                PassengerType passengerType = new PassengerType();
                passengerType.setPassengerId(buildId(passenger));
                passengerType.setPassengerName(
                    OrderCreateProcessorOfUtil.getUseName(passenger, orderCreateRequestType.getCityInput().getCityId(),
                        checkAvailInfo, qconfigOfCertificateInitConfig));
                passengerType.setIsEmployee(
                    "T".equalsIgnoreCase(passenger.getHotelPassengerInput().getEmployee()) ? 1 : 0);
                passengerType.setCostCenters(null);
                passengerTypeList.add(passengerType);
            }
        }
        return passengerTypeList;
    }

    private String buildId(HotelBookPassengerInput hotelBookPassengerInput) {
        return StringUtil.isNotBlank(hotelBookPassengerInput.getHotelPassengerInput().getUid()) ?
            hotelBookPassengerInput.getHotelPassengerInput().getUid() :
            hotelBookPassengerInput.getHotelPassengerInput().getInfoId();
    }

    private HotelBookPassengerInput buildHotelBookPassengerInputById(List<HotelBookPassengerInput> passengers,
        String uid) {
        List<HotelBookPassengerInput> collect =
            passengers.stream().filter(psg -> uid.equalsIgnoreCase(buildId(psg))).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(collect)) {
            return null;
        }
        return collect.get(0);
    }

    /**
     * 根据后台查询结果，匹配出成本中心扩展字段
     *
     * @param costCenterInfoResponse
     * @return
     */
    private CostCenterExtItemType buildCostCenterExtItem(CostCenterInfoType costCenterInfoResponse,
        CostCenterResourceEnum costCenterResourceEnum, String costCenterValue,
        OrderCreateRequestType orderCreateRequestType) {
        boolean chineseSimple =
            "zh-CN".equalsIgnoreCase(orderCreateRequestType.getIntegrationSoaRequestType().getLanguage());
        CostCenterExtConfigType costCenterExtItem = buildCostCenterExt(costCenterInfoResponse, costCenterResourceEnum);
        if (ObjectUtil.isNull(costCenterExtItem) || (!CollectionUtil.isEmpty(costCenterExtItem.getCostCenterExtItems())
            && !costCenterExtItem.getCostCenterExtItems().stream().anyMatch(
            cce -> StringUtil.compareIgnoreCase(StringUtil.trimToEmpty(costCenterValue),
                StringUtil.trimToEmpty(chineseSimple ? cce.getCostCenterExtName() : cce.getCostCenterExtNameEn()))))) {
            if (StringUtils.isBlank(costCenterValue)) {
                return null;
            }
        } else if (CollectionUtil.isEmpty(costCenterExtItem.getCostCenterExtItems())) {
            // 如果后台仅启用了扩展字段，但是未配置具体项时，前端展示为文本框
            CostCenterExtItemType costCenterExtItemTypeTO = new CostCenterExtItemType();
            if (chineseSimple) {
                costCenterExtItemTypeTO.setCostCenterExtName(costCenterValue);
            } else {
                costCenterExtItemTypeTO.setCostCenterExtNameEn(costCenterValue);
            }
            costCenterExtItemTypeTO.setCostCenterExtCode("");
            costCenterExtItemTypeTO.setIsDefault(false);
            costCenterExtItemTypeTO.setCostCenterExtId(0L);
            return costCenterExtItemTypeTO;
        }
        if (costCenterExtItem == null) {
            return null;
        }
        CostCenterExtItemType costCenterExtItemType = costCenterExtItem.getCostCenterExtItems().stream().filter(
                cce -> StringUtil.compareIgnoreCase(StringUtil.trimToEmpty(costCenterValue), StringUtil.trimToEmpty(
                    buildValueByLanguage(cce.getCostCenterExtNameEn(), cce.getCostCenterExtName(),
                        orderCreateRequestType.getIntegrationSoaRequestType().getLanguage())))).toList().stream()
            .findFirst().orElse(null);
        return costCenterExtItemType;
    }

    private String buildValueByLanguage(String costCenterEn, String costCenter, String local) {
        if ("zh-CN".equalsIgnoreCase(local)) {
            return costCenter;
        }
        return costCenterEn;
    }

    /**
     * 根据后台查询结果，匹配出成本中心扩展配置
     *
     * @param costCenterInfoResponse
     * @return
     */
    public CostCenterExtConfigType buildCostCenterExt(CostCenterInfoType costCenterInfoResponse,
        CostCenterResourceEnum costCenterResourceEnum) {
        if (costCenterInfoResponse == null || CollectionUtil.isEmpty(costCenterInfoResponse.getCostCenterExtlist())) {
            return null;
        }
        String costCenterFindKey = costCenterResourceEnum.getExtName();
        List<CostCenterExtConfigType> findCostCenterExt = costCenterInfoResponse.getCostCenterExtlist().stream()
            .filter(cce -> costCenterFindKey.equalsIgnoreCase(cce.getCostCenterExtType())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(findCostCenterExt)) {
            return null;
        }
        return findCostCenterExt.get(0);
    }

    private String buildExtItemTitle(Optional<CostCenterExtConfigType> extItem,
        CostCenterResourceEnum costCenterInputKeysEnum) {
        if (extItem.isPresent() && StringUtils.isNotBlank(extItem.get().getCostCenterExtTitle())) {
            return extItem.get().getCostCenterExtTitle();
        } else {
            return costCenterInputKeysEnum.buildDefaultTitle();
        }
    }

    private String buildExtItemTitleEn(Optional<CostCenterExtConfigType> extItem,
        CostCenterResourceEnum costCenterInputKeysEnum) {
        if (extItem.isPresent() && StringUtils.isNotBlank(extItem.get().getCostCenterExtTitleEn())) {
            return extItem.get().getCostCenterExtTitleEn();
        } else {
            return costCenterInputKeysEnum.buildDefaultTitleEn();
        }
    }

    private int buildBizType(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
        if (checkAvailInfo.getRoomTypeEnum() == RoomTypeEnum.C) {
            return BIZ_TYPE_C_HOTEL;
        }
        return BIZ_TYPE_M_HOTEL;
    }

    private Optional<CostCenterExtConfigType> buildExtInfo(CostCenterInfoType costCenterResponse, String extName) {
        if (costCenterResponse == null || StringUtils.isBlank(extName) || CollectionUtil.isEmpty(
            costCenterResponse.getCostCenterExtlist())) {
            return Optional.empty();
        }
        List<CostCenterExtConfigType> configdExtInfo = costCenterResponse.getCostCenterExtlist().stream()
            .filter(cce -> StringUtils.equalsIgnoreCase(extName, cce.getCostCenterExtType()))
            .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(configdExtInfo)) {
            return Optional.empty();
        }
        return Optional.of(configdExtInfo.get(0));
    }

}
