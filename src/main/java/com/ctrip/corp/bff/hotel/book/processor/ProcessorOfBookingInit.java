package com.ctrip.corp.bff.hotel.book.processor;

import com.ctrip.basebiz.geolocation.service.GetAllNationalityRequestType;
import com.ctrip.basebiz.geolocation.service.GetAllNationalityResponseType;
import com.ctrip.basebiz.iplocation.proto.GetIpInfoRequestTypeV2;
import com.ctrip.basebiz.iplocation.proto.GetIpInfoResponseTypeV2;
import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListRequestType;
import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListResponseType;
import com.ctrip.corp.agg.commonws.pkg.room.snapshot.entity.GetPackageRoomSnapshotRequestType;
import com.ctrip.corp.agg.commonws.pkg.room.snapshot.entity.GetPackageRoomSnapshotResponseType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailRequestType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem;
import com.ctrip.corp.agg.hotel.salestrategy.entity.CalculateTravelRewardsRequestType;
import com.ctrip.corp.agg.hotel.salestrategy.entity.CalculateTravelRewardsResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyRequestType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.entity.GetHotelTravelPolicyRequestType;
import com.ctrip.corp.agg.hotel.tmc.entity.GetHotelTravelPolicyResponseType;
import com.ctrip.corp.bff.framework.hotel.common.builder.GetTravelPolicyRequest;
import com.ctrip.corp.bff.framework.hotel.common.builder.SearchApprovalRequest;
import com.ctrip.corp.bff.framework.hotel.common.enums.TrackingBookModeEnum;
import com.ctrip.corp.bff.framework.hotel.common.enums.TrackingEnum;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfGeneralSearchAccountInfoRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfGetCityBaseInfoRequestType;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfGetCorpUserInfoRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfGetHotelTravelPolicyRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfGetIpInfoRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfGetSubAccountConfigRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfPolicyGeneralSearchAccountInfoRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfSearchApprovalRequest;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfPersonalAccountConfig;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfRoomNumberInfoConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfSearchApproval;
import com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalOutput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPositionInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.OriginalOrderInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.utils.ResourceTokenUtil;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.qconfig.QConfigUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.PosEnum;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceTokenInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.hotel.book.common.builder.BookingInitAssembleRequest;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.BookingInitErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfCheckTravelPolicyRequestType;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfGetPlatformRelationByUidRequestType;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfPayConfigRequestType;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfQueryIndividualAccountRequestType;
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TrackingUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckTravelPolicy;
import com.ctrip.corp.bff.hotel.book.contract.BookingHotelInfo;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitResponseType;
import com.ctrip.corp.bff.hotel.book.contract.BookingRoomInfo;
import com.ctrip.corp.bff.hotel.book.contract.RoomProperty;
import com.ctrip.corp.bff.hotel.book.handler.corp4jservice.HandlerOfGetReasoncodes;
import com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice.HandlerOfGeneralSearchAccountInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice.HandlerOfGetAuthDelayConfig;
import com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice.HandlerOfQueryIndividualAccount;
import com.ctrip.corp.bff.hotel.book.handler.corpagghotelexpenseservice.HandlerOfCalculateServiceChargeV2;
import com.ctrip.corp.bff.hotel.book.handler.corpagghotelexpenseservice.HandlerOfGetRoomChargePolicyList;
import com.ctrip.corp.bff.hotel.book.handler.corpagghotelsalestrategyservice.HandlerOfCalculateTravelRewards;
import com.ctrip.corp.bff.hotel.book.handler.corpagghoteltmcservice.HandlerOfCheckTravelPolicy;
import com.ctrip.corp.bff.hotel.book.handler.corpagghoteltmcservice.HandlerOfGetHotelTravelPolicy;
import com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice.HandlerOfApprovalTextInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice.HandlerOfBatchApprovalDefault;
import com.ctrip.corp.bff.hotel.book.handler.corpbfftoolsservice.HandlerOfCountryQuery;
import com.ctrip.corp.bff.hotel.book.handler.corpconfigurationservice.HandlerOfGetSubAccountConfig;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws.HandlerOfDistributePaymentAmount;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws.HandlerOfGetCancelPolicyDesc;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws.HandlerOfGetPackageRoomList;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws.HandlerOfGetPackageRoomSnapshot;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws.HandlerOfGetSupportedInvoiceType;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws.HandlerOfGetSupportedPaymentMethod;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookqueryservice.HandlerOfGetCityBaseInfo;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookqueryservice.HandlerOfGetHotelDetailInfo;
import com.ctrip.corp.bff.hotel.book.handler.corphotelmemberservice.HandlerOfSearchRegistrationFields;
import com.ctrip.corp.bff.hotel.book.handler.corphotelorderdetailservice.HandlerOfGetCorpHotelOrderDetail;
import com.ctrip.corp.bff.hotel.book.handler.corphotelorderdetailservice.HandlerOfQueryOrderSettings;
import com.ctrip.corp.bff.hotel.book.handler.corphotelroomavailableservice.HandlerOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.handler.corpsettlementinvoiceservice.HandlerOfCorpOrderInvoiceInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.HandlerOfGetContactInvoiceDefaultInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.HandlerOfGetCorpUserHotelVipCard;
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.HandlerOfGetCorpUserInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.HandlerOfGetPlatformRelationByUid;
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.HandlerOfGetUserAddressInfo;
import com.ctrip.corp.bff.hotel.book.handler.geolocationservice.HandlerOfGetAllNationality;
import com.ctrip.corp.bff.hotel.book.handler.handlerofgroup4jservice.HandlerOfQueryBizModeBindRelation;
import com.ctrip.corp.bff.hotel.book.handler.htlconfigurationmiddleservice.HandlerOfGetGroupVipPackage;
import com.ctrip.corp.bff.hotel.book.handler.invoiceservice.HandlerOfGetInvoiceTitleList;
import com.ctrip.corp.bff.hotel.book.handler.iplocationservice.HandlerOfGetIpInfo;
import com.ctrip.corp.bff.hotel.book.handler.orderdataaggregationqueryservice.HandlerOfQueryHotelOrderData;
import com.ctrip.corp.bff.hotel.book.handler.ordermanagementcenterhotelservice.HandlerOfXProductEnquire;
import com.ctrip.corp.bff.hotel.book.handler.orderpaymentcentertransactionservice.HandlerOfPayConfig;
import com.ctrip.corp.bff.hotel.book.handler.orderreimbursementservice.HandlerOfReimbursementQuery;
import com.ctrip.corp.bff.hotel.book.handler.preapprovalservice.HandlerOfSearchApproval;
import com.ctrip.corp.bff.hotel.book.handler.rewardservice.HandlerOfQueryMrgMemberUserInfo;
import com.ctrip.corp.bff.hotel.book.handler.triporderservice.HandlerOfGetTripBookingInfos;
import com.ctrip.corp.bff.hotel.book.handler.triporderservice.HandlerOfSearchTripBasicInfo;
import com.ctrip.corp.bff.hotel.book.handler.triporderservice.HandlerOfSearchTripDetail;
import com.ctrip.corp.bff.hotel.book.handler.userinfoqueryservice.HandlerOfBatchSearchClientsInfo;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfApprovalOutput;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfApprovalTextInfoRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfBatchApprovalDefaultRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfBatchSearchClientsInfoRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfBuildTravelPolicyRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfCalculateServiceChargeV2RequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfCalculateTravelRewardsRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfCheckAvailRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfCorpOrderInvoiceDetailInfoQueryRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfDistributePaymentAmountRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetCancelPolicyDescRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetContactInvoiceDefaultInfoRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetCorpUserHotelVipCardRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetCorpUserInfoByPolicyRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetGroupVipPackageRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetHotelDetailInfoRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetInvoiceTitlesRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetPackageRoomListRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetPackageRoomSnapshotRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetRoomChargePolicyListRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetSupportedInvoiceRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetSupportedPaymentMethodRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetTripBookingInfosRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetUserAddressInfoRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfQueryCtripMrgMemberUserInfoRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfQueryHotelOrderDataRequest;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfQueryOrderSettingsRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfReimbursementQueryRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfSearchRegistrationFieldsRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfXProductEnquireRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.*;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.response.MapperOfBookingInitResponse;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.MapperOfGetAuthDelayRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.MapperOfGetReasonCodesRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.MapperOfOrderDetailRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.MapperOfSearchTripBasicInfoRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.MapperOfSearchTripDetailRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.*;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.MemberBonusRuleEntry;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoRequestType;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType;
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultRequestType;
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultResponseType;
import com.ctrip.corp.bff.tools.contract.CountryQueryRequestType;
import com.ctrip.corp.bff.tools.contract.CountryQueryResponseType;
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigRequestType;
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalRequestType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalResponseType;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoRequestType;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;
import com.ctrip.corp.hotel.book.query.entity.GetHotelDetailInfoRequestType;
import com.ctrip.corp.hotel.book.query.entity.GetHotelDetailInfoResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.CancelPolicyDescType;
import com.ctrip.corp.hotelbook.commonws.entity.DistributePaymentAmountRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.DistributePaymentAmountResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedInvoiceTypeRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedInvoiceTypeResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.ApprovalType;
import com.ctrip.corp.order.data.aggregation.query.contract.HotelInfoType;
import com.ctrip.corp.order.data.aggregation.query.contract.HotelProductType;
import com.ctrip.corp.order.data.aggregation.query.contract.HotelType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataRequestType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.TravelInfoType;
import com.ctrip.corp.order.managementcenter.hotel.service.contract.XProductEnquireRequestType;
import com.ctrip.corp.order.managementcenter.hotel.service.contract.XProductEnquireResponseType;
import com.ctrip.hotel.order.rewardservice.contract.CtripMemberUserInfoResponse;
import com.ctrip.hotel.order.rewardservice.contract.QueryCtripMrgMemberUserInfoRequest;
import com.ctrip.hotel.wireless.htlgetgroupvippackagefunction.soa.contract.GetGroupVipPackageRequestType;
import com.ctrip.hotel.wireless.htlgetgroupvippackagefunction.soa.contract.GetGroupVipPackageResponseType;
import com.ctrip.ibu.member.coupon.platform.client.GetMultilingualCouponInfoRequestType;
import com.ctrip.ibu.member.coupon.platform.client.GetMultilingualCouponInfoResponseType;
import com.ctrip.model.CalculateServiceChargeV2RequestType;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;
import com.ctrip.model.GetCancelPolicyDescRequestType;
import com.ctrip.model.GetCancelPolicyDescResponseType;
import com.ctrip.model.GetRoomChargePolicyListRequestType;
import com.ctrip.model.GetRoomChargePolicyListResponseType;
import com.ctrip.model.SearchRegistrationFieldsRequestType;
import com.ctrip.model.SearchRegistrationFieldsResponseType;
import com.ctrip.order.reimbursement.ReimbursementDetailInfoType;
import com.ctrip.order.reimbursement.ReimbursementInfoType;
import com.ctrip.order.reimbursement.ReimbursementQueryRequestType;
import com.ctrip.order.reimbursement.ReimbursementQueryResponseType;
import com.ctrip.soa._21234.GetTripBookingInfosRequestType;
import com.ctrip.soa._21234.GetTripBookingInfosResponseType;
import com.ctrip.soa._21234.SearchTripBasicInfoRequestType;
import com.ctrip.soa._21234.SearchTripBasicInfoResponseType;
import com.ctrip.soa._21234.SearchTripDetailRequestType;
import com.ctrip.soa._21234.SearchTripDetailResponseType;
import com.ctrip.soa._21685.PayConfigRequestType;
import com.ctrip.soa._21685.PayConfigResponseType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailRequestType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailResponseType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.QueryOrderSettingsRequestType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.QueryOrderSettingsResponseType;
import com.ctrip.soa.platform.sps.InvoiceService.v1.GetInvoiceTitlesRequest;
import com.ctrip.soa.platform.sps.InvoiceService.v1.GetInvoiceTitlesResponse;
import corp.settlement.service.invoice.corpinvoice.CorpOrderInvoiceDetailInfoQueryRequest;
import corp.settlement.service.invoice.corpinvoice.CorpOrderInvoiceDetailInfoQueryResponse;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import corp.user.service.CorpAccountQueryService.GetAuthDelayRequestType;
import corp.user.service.CorpAccountQueryService.GetAuthDelayResponseType;
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountRequestType;
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType;
import corp.user.service.UserInfoQueryService.BatchSearchClientsInfoRequestType;
import corp.user.service.UserInfoQueryService.BatchSearchClientsInfoResponseType;
import corp.user.service.corp4jservice.GetReasoncodesRequestType;
import corp.user.service.corp4jservice.GetReasoncodesResponseType;
import corp.user.service.corpUserInfoService.GetContactInvoiceDefaultInfoRequestType;
import corp.user.service.corpUserInfoService.GetContactInvoiceDefaultInfoResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserHotelVipCardRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserHotelVipCardResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import corp.user.service.corpUserInfoService.GetPlatformRelationByUidRequestType;
import corp.user.service.corpUserInfoService.GetPlatformRelationByUidResponseType;
import corp.user.service.corpUserInfoService.GetUserAddressInfoRequestType;
import corp.user.service.corpUserInfoService.GetUserAddressInfoResponseType;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationRequestType;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/20 0:11
 */
@Component
public class ProcessorOfBookingInit extends AbstractProcessor<BookingInitRequestType, BookingInitResponseType> {
    // region 注入
    @Autowired
    private MapperOfBatchApprovalDefaultRequest mapperOfBatchApprovalDefaultRequest;
    @Autowired
    private HandlerOfBatchApprovalDefault handlerOfBatchApprovalDefault;
    @Autowired
    private HandlerOfApprovalTextInfo handlerOfApprovalTextInfo;
    @Autowired
    private MapperOfApprovalTextInfoRequestType mapperOfApprovalTextInfoRequestType;
    @Autowired
    private MapperOfGeneralSearchAccountInfoRequest mapperOfGeneralSearchAccountInfoRequest;
    @Autowired
    private HandlerOfGeneralSearchAccountInfo handlerOfGeneralSearchAccountInfo;
    @Autowired
    private HandlerOfGetCorpUserInfo handlerOfGetCorpUserInfo;
    @Autowired
    private MapperOfGetCorpUserInfoRequest mapperOfGetCorpUserInfoRequest;
    @Autowired
    private HandlerOfGetSubAccountConfig handlerOfGetSubAccountConfig;
    @Autowired
    private MapperOfGetSubAccountConfigRequest mapperOfGetSubAccountConfigRequest;
    @Autowired
    private MapperOfApprovalOutput mapperOfApprovalOutput;
    @Autowired
    private HandlerOfQueryHotelOrderData handlerOfQueryHotelOrderData;
    @Autowired
    private MapperOfQueryHotelOrderDataRequest mapperOfQueryHotelOrderDataRequest;
    @Autowired
    private HandlerOfQueryOrderSettings handlerOfQueryOrderSettings;
    @Autowired
    private MapperOfQueryOrderSettingsRequestType mapperOfQueryOrderSettingsRequestType;
    @Autowired
    private HandlerOfSearchApproval handlerOfSearchApproval;
    @Autowired
    private MapperOfSearchApprovalRequest mapperOfSearchApprovalRequest;
    @Autowired
    private HandlerOfGetHotelTravelPolicy handlerOfGetHotelTravelPolicy;
    @Autowired
    private MapperOfGetHotelTravelPolicyRequest mapperOfGetHotelTravelPolicyRequest;
    @Autowired
    private MapperOfBuildTravelPolicyRequestType mapperOfBuildTravelPolicyRequestType;
    @Autowired
    private HandlerOfCheckAvail handlerOfCheckAvail;
    @Autowired
    private MapperOfCheckAvailRequest mapperOfCheckAvailRequest;
    @Autowired
    private MapperOfGetIpInfoRequest mapperOfGetIpInfoRequest;
    @Autowired
    private HandlerOfGetIpInfo handlerOfGetIpInfo;
    @Autowired
    private HandlerOfCalculateServiceChargeV2 handlerOfCalculateServiceChargeV2;
    @Autowired
    private MapperOfCalculateServiceChargeV2RequestType mapperOfCalculateServiceChargeV2Request;
    @Autowired
    private HandlerOfGetSupportedPaymentMethod handlerOfGetSupportedPaymentMethod;
    @Autowired
    private MapperOfGetSupportedPaymentMethodRequestType mapperOfGetSupportedPaymentMethodRequestType;
    @Autowired
    private HandlerOfReimbursementQuery handlerOfReimbursementQuery;
    @Autowired
    private MapperOfReimbursementQueryRequestType mapperOfReimbursementQueryRequestType;
    @Autowired
    private HandlerOfGetSupportedInvoiceType handlerOfGetSupportedInvoiceType;
    @Autowired
    private MapperOfGetSupportedInvoiceRequestType mapperOfGetSupportedInvoiceRequestType;
    @Autowired
    private HandlerOfGetContactInvoiceDefaultInfo handlerOfGetContactInvoiceDefaultInfo;
    @Autowired
    private MapperOfGetContactInvoiceDefaultInfoRequest mapperOfGetContactInvoiceDefaultInfoRequest;
    @Autowired
    private HandlerOfGetInvoiceTitleList handlerOfGetInvoiceTitleList;
    @Autowired
    private MapperOfGetInvoiceTitlesRequest mapperOfGetInvoiceTitlesRequest;
    @Autowired
    private HandlerOfGetUserAddressInfo handlerOfGetUserAddressInfo;
    @Autowired
    private MapperOfGetUserAddressInfoRequestType mapperOfGetUserAddressInfoRequestType;
    @Autowired
    private HandlerOfCalculateTravelRewards handlerOfCalculateTravelRewards;
    @Autowired
    private MapperOfCalculateTravelRewardsRequest mapperOfCalculateTravelRewardsRequest;
    @Autowired
    private HandlerOfDistributePaymentAmount handlerOfDistributePaymentAmount;
    @Autowired
    private MapperOfDistributePaymentAmountRequest mapperOfDistributePaymentAmountRequest;
    @Autowired
    private HandlerOfGetRoomChargePolicyList handlerOfGetRoomChargePolicyList;
    @Autowired
    private MapperOfGetRoomChargePolicyListRequestType mapperOfGetRoomChargePolicyListRequestType;
    @Autowired
    private HandlerOfGetPlatformRelationByUid handlerOfGetPlatformRelationByUid;
    @Autowired
    private MapperOfGetPlatformRelationByUidRequestType mapperOfGetPlatformRelationByUidRequestType;
    @Autowired
    private HandlerOfGetGroupVipPackage handlerOfGetGroupVipPackage;
    @Autowired
    private MapperOfGetGroupVipPackageRequestType mapperOfGetGroupVipPackageRequestType;
    @Autowired
    private HandlerOfQueryMrgMemberUserInfo handlerOfQueryMrgMemberUserInfo;
    @Autowired
    private MapperOfQueryCtripMrgMemberUserInfoRequest mapperOfQueryCtripMrgMemberUserInfoRequest;
    @Autowired
    private HandlerOfXProductEnquire handlerOfXProductEnquire;
    @Autowired
    private MapperOfXProductEnquireRequestType mapperOfXProductEnquireRequestType;
    @Autowired
    private HandlerOfGetCorpUserHotelVipCard handlerOfGetCorpUserHotelVipCard;
    @Autowired
    private MapperOfGetCorpUserHotelVipCardRequestType mapperOfGetCorpUserHotelVipCardRequestType;
    @Autowired
    private HandlerOfGetAuthDelayConfig handlerOfGetAuthDelayConfig;
    @Autowired
    private MapperOfGetAuthDelayRequestType mapperOfGetAuthDelayRequestType;
    @Autowired
    private HandlerOfGetCityBaseInfo handlerOfGetCityBaseInfo;
    @Autowired
    private MapperOfGetCityBaseInfoRequestType mapperOfGetCityBaseInfoRequestType;
    @Autowired
    private HandlerOfGetCancelPolicyDesc handlerOfGetCancelPolicyDesc;
    @Autowired
    private MapperOfGetCancelPolicyDescRequest mapperOfGetCancelPolicyDescRequest;
    @Autowired
    private MapperOfBookingInitResponse mapperOfBookingInitResponse;
    @Autowired
    private HandlerOfQueryIndividualAccount handlerOfQueryIndividualAccount;
    @Autowired
    private MapperOfQueryIndividualAccountRequestType mapperOfQueryIndividualAccountRequestType;
    @Autowired
    private MapperOfGetCorpUserInfoByPolicyRequest mapperOfGetCorpUserInfoByPolicyRequest;
    @Autowired
    private QConfigOfCodeMappingConfig qConfigOfCodeMappingConfig;
    @Autowired
    private MapperOfGetPackageRoomListRequestType mapperOfGetPackageRoomListRequestType;
    @Autowired
    private MapperOfGetPackageRoomSnapshotRequestType mapperOfGetPackageRoomSnapshotRequestType;
    @Autowired
    private HandlerOfGetPackageRoomList handlerOfGetPackageRoomList;
    @Autowired
    private HandlerOfGetPackageRoomSnapshot handlerOfGetPackageRoomSnapshot;
    @Autowired
    private QConfigOfPersonalAccountConfig qConfigOfPersonalAccountConfig;
    @Autowired
    private QConfigOfRoomNumberInfoConfig qConfigOfRoomNumberInfoConfig;
    @Autowired
    private HandlerOfGetHotelDetailInfo handlerOfGetHotelDetailInfo;
    @Autowired
    private MapperOfGetHotelDetailInfoRequest mapperOfGetHotelDetailInfoRequest;
    @Autowired
    private HandlerOfSearchRegistrationFields handlerOfSearchRegistrationFields;
    @Autowired
    private MapperOfSearchRegistrationFieldsRequestType mapperOfSearchRegistrationFieldsRequestType;
    @Autowired
    private HandlerOfBatchSearchClientsInfo handlerOfBatchSearchClientsInfo;
    @Autowired
    private MapperOfBatchSearchClientsInfoRequestType mapperOfBatchSearchClientsInfoRequestType;
    @Autowired
    private QConfigOfContactConfig qConfigOfContactConfig;
    @Autowired
    private HandlerOfCheckTravelPolicy handlerOfCheckTravelPolicy;
    @Autowired
    private MapperOfCheckTravelPolicyRequestType mapperOfCheckTravelPolicyRequestType;
    @Autowired
    private MapperOfGetTripBookingInfosRequest mapperOfGetTripBookingInfosRequest;
    @Autowired
    private HandlerOfGetTripBookingInfos handlerOfGetTripBookingInfos;
    @Autowired
    private QconfigOfGroupHotelMemberCardRuleConfig qconfigOfGroupHotelMemberCardRuleConfig;
    @Autowired
    private HandlerOfSearchTripDetail handlerOfSearchTripDetail;
    @Autowired
    private MapperOfSearchTripDetailRequestType mapperOfSearchTripDetailRequestType;
    @Autowired
    private MapperOfPayConfigRequestType mapperOfPayConfigRequestType;
    @Autowired
    private HandlerOfPayConfig handlerOfPayConfig;
    @Autowired
    private HandlerOfQueryBizModeBindRelation handlerOfQueryBizModeBindRelation;
    @Autowired
    private MapperOfQueryBizModeBindRelationRequestType mapperOfQueryBizModeBindRelationRequestType;
    @Autowired
    private HandlerOfGetReasoncodes handlerOfGetReasoncodes;
    @Autowired
    private MapperOfGetReasonCodesRequestType mapperOfGetReasonCodesRequestType;
    @Autowired
    private MapperOfPolicyGeneralSearchAccountInfoRequest mapperOfPolicyAccountInfoRequest;
    @Autowired
    private QConfigOfCustomizedSharkConfig qConfigOfCustomizedSharkConfig;
    @Autowired
    private HandlerOfCorpOrderInvoiceInfo handlerOfCorpOrderInvoiceInfo;
    @Autowired
    private MapperOfCorpOrderInvoiceDetailInfoQueryRequest mapperOfCorpOrderInvoiceDetailInfoQueryRequest;
    @Autowired
    private QconfigOfRegisterConfig qconfigOfRegisterConfig;
    @Autowired
    private MapperOfCountryQueryRequestType mapperOfCountryQueryRequestType;
    @Autowired
    private HandlerOfCountryQuery handlerOfCountryQuery;
    @Autowired
    private MapperOfGetAllNationalityRequestType mapperOfGetAllNationalityRequestType;
    @Autowired
    private HandlerOfGetAllNationality handlerOfGetAllNationality;
    @Autowired
    private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;
    @Autowired
    private MapperOfSearchTripBasicInfoRequestType mapperOfSearchTripBasicInfoRequestType;
    @Autowired
    private HandlerOfSearchTripBasicInfo handlerOfSearchTripBasicInfo;
    // endregion
    /** 一个房型如果有多个信息，用 | 分割 */
    private static final String ROOM_PROPERTY_SEPARATOR = "|";

    // 会员卡号规则
    private static final String QCONFIG_FILE_MEMBER_BONUS_RULE = "memberBonusRule.json";
    @Override
    public BookingInitResponseType execute(BookingInitRequestType requestType) throws Exception {
        ResourceToken resourceToken = ResourceTokenUtil.tryParseToken(
            Optional.ofNullable(requestType.getResourceTokenInfo()).map(ResourceTokenInfo::getResourceToken).orElse(null));
        BookInitToken bookInitToken = TokenParseUtil.parseToken(requestType.getBookInitToken(), BookInitToken.class);
        // TODO 确认入参orderId是否还在使用，需下线
        Long orderId = getOrderId(requestType, resourceToken);
        Map<String, StrategyInfo> strategyInfoMap =
            StrategyOfBookingInitUtil.buildStrategyInfoMap(requestType.getStrategyInfos());

        WaitFuture<ApprovalTextInfoRequestType, ApprovalTextInfoResponseType> approvalTextInfoResponseTypeWaitFuture =
            handlerOfApprovalTextInfo.handleAsync(mapperOfApprovalTextInfoRequestType.map(
                Tuple2.of(requestType.getIntegrationSoaRequestType(), requestType.getStrategyInfos())));
        WaitFuture<GeneralSearchAccountInfoRequestType, GeneralSearchAccountInfoResponseType> generalSearchAccountInfoResponseTypeWaitFuture =
            handlerOfGeneralSearchAccountInfo.handleAsync(mapperOfGeneralSearchAccountInfoRequest
                .map(Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // 个人账户开关
        WaitFuture<QueryIndividualAccountRequestType, QueryIndividualAccountResponseType>
            queryIndividualAccountWaitFuture = handlerOfQueryIndividualAccount.handleAsync(
            mapperOfQueryIndividualAccountRequestType.map(Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // 修改场景，根据订单号查询审批单
        WrapperOfSearchApproval.ApprovalInfo approvalInfo = null;
        WaitFuture<QueryHotelOrderDataRequestType, QueryHotelOrderDataResponseType> queryHotelOrderDataResponseTypeWaitFuture = null;
        QueryHotelOrderDataRequestType queryHotelOrderDataRequestType = mapperOfQueryHotelOrderDataRequest.map(
            Tuple2.of(requestType.getIntegrationSoaRequestType(), orderId));
        if (BookingInitProcessorOfUtil.needQueryHotelOrderData(orderId)) {
            queryHotelOrderDataResponseTypeWaitFuture =
                handlerOfQueryHotelOrderData.handleAsync(queryHotelOrderDataRequestType);
        }
        // ============================== level1 ==============================
        // 获取用户信息
        WaitFuture<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType> getCorpUserInfoResponseTypeWaitFuture =
            handlerOfGetCorpUserInfo.handleAsync(mapperOfGetCorpUserInfoRequest.map(Tuple1.of(requestType.getIntegrationSoaRequestType())));
        GetCorpUserInfoResponseType getCorpUserInfoResponseType = getCorpUserInfoResponseTypeWaitFuture.get();
        // 查询子账户信息
        WaitFuture<GetSubAccountConfigRequestType, GetSubAccountConfigResponseType> getSubAccountConfigResponseTypeWaitFuture =
            handlerOfGetSubAccountConfig.handleAsync(mapperOfGetSubAccountConfigRequest.map(Tuple1.of(getCorpUserInfoResponseType)));
        // 双付丰享支付配置查询
        WaitFuture<PayConfigRequestType, PayConfigResponseType> payConfigResponseTypeWaitFuture =
                handlerOfPayConfig.handleAsync(mapperOfPayConfigRequestType.map(Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // 查询RC信息
        WaitFuture<GetReasoncodesRequestType, GetReasoncodesResponseType> getReasoncodesResponseTypeWaitFuture = null;
        if (StrategyOfBookingInitUtil.hotelCheckAvail(requestType.getStrategyInfos()) || StrategyOfBookingInitUtil.needRcInfo(requestType.getStrategyInfos())) {
            getReasoncodesResponseTypeWaitFuture = handlerOfGetReasoncodes.handleAsync(
                mapperOfGetReasonCodesRequestType.map(Tuple1.of(requestType.getIntegrationSoaRequestType())));
        }
        // 查询政策执行人账户信息
        WaitFuture<GeneralSearchAccountInfoRequestType, GeneralSearchAccountInfoResponseType> policyAccountInfoResponseTypeWaitFuture = null;
        PolicyInput policyInput = requestType.getPolicyInput();
        if (policyInput != null && StringUtil.isNotBlank(policyInput.getPolicyUid())) {
            // 查询政策执行人账户信息
            policyAccountInfoResponseTypeWaitFuture = handlerOfGeneralSearchAccountInfo.handleAsync(
                mapperOfPolicyAccountInfoRequest.map(Tuple2.of(requestType.getIntegrationSoaRequestType(), policyInput)));
        }
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
            .accountInfo(generalSearchAccountInfoResponseTypeWaitFuture.get())
            .policyAccountInfo(WaitFutureUtil.safeGetFuture(policyAccountInfoResponseTypeWaitFuture))
            // ============================== level2 ==============================
            .corpUserInfo(WaitFutureUtil.safeGetFuture(getCorpUserInfoResponseTypeWaitFuture))
            .subAccountConfig(WaitFutureUtil.safeGetFuture(getSubAccountConfigResponseTypeWaitFuture))
            .build();
        WaitFuture<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType> getCorpUserInfoByPolicyResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetCorpUserInfo(requestType, accountInfo)) {
            getCorpUserInfoByPolicyResponseTypeWaitFuture = handlerOfGetCorpUserInfo.handleAsync(mapperOfGetCorpUserInfoByPolicyRequest
                .map(Tuple1.of(requestType)));
        }
        WaitFuture<BatchApprovalDefaultRequestType, BatchApprovalDefaultResponseType> batchApprovalDefaultResponseTypeWaitFuture = null;
        // 仅因公付场景下，查询默认审批单
        if (BookingInitProcessorOfUtil.needBatchApprovalDefault(requestType, accountInfo, resourceToken)) {
            batchApprovalDefaultResponseTypeWaitFuture =
                    handlerOfBatchApprovalDefault.handleAsync(mapperOfBatchApprovalDefaultRequest.map(Tuple2.of(requestType, accountInfo)));
        }

        // NOTE:下游很多地方用到，上线前修改，避免空指针
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType =
            WaitFutureUtil.safeGetFuture(queryHotelOrderDataResponseTypeWaitFuture);

        WaitFuture<SearchTripBasicInfoRequestType, SearchTripBasicInfoResponseType>
            searchTripBasicInfoResponseTypeWaitFutureOfOriginalOrder = null;
        if (BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(queryHotelOrderDataResponseType)) {
            SearchTripBasicInfoRequestType searchTripBasicInfoRequestType = mapperOfSearchTripBasicInfoRequestType.map(
                Tuple2.of(queryHotelOrderDataResponseType.getOrderBasicInfo().getTripOrderId(),
                    requestType.getIntegrationSoaRequestType()));
            searchTripBasicInfoResponseTypeWaitFutureOfOriginalOrder =
                handlerOfSearchTripBasicInfo.handleAsync(searchTripBasicInfoRequestType);
        }
        if (BookingInitProcessorOfUtil.needSearchApproval(requestType)) {
            SearchApprovalRequest adapterOfSearchApprovalRequest =
                buildSearchApprovalRequest(requestType, queryHotelOrderDataResponseType, resourceToken, accountInfo);
            if (adapterOfSearchApprovalRequest != null) {WaitFuture<SearchApprovalRequestType, SearchApprovalResponseType> searchApprovalResponseTypeWaitFuture =
                handlerOfSearchApproval.handleAsync(mapperOfSearchApprovalRequest.map(Tuple1.of(adapterOfSearchApprovalRequest)));
                // ============================== level3 ==============================
                approvalInfo = WrapperOfSearchApproval.builder().searchApprovalResponseType(searchApprovalResponseTypeWaitFuture.getWithoutError()).build();
            }
        }
        HotelPayTypeEnum selectedRoomPayType = HotelPayTypeUtil.getSelectedRoomPayType(requestType, queryHotelOrderDataResponseType);
        ApprovalOutput approvalOutput = mapperOfApprovalOutput.map(
                Tuple7.of(requestType, WaitFutureUtil.safeGetFutureWithoutError(batchApprovalDefaultResponseTypeWaitFuture), approvalInfo,
                        queryHotelOrderDataResponseType, resourceToken, accountInfo, approvalTextInfoResponseTypeWaitFuture.getWithoutError()));
        BookingInitResponseType bookInitResponseType = new BookingInitResponseType();
        bookInitResponseType.setApprovalOutput(approvalOutput);
        if (!StrategyOfBookingInitUtil.fromBookingInit(requestType.getStrategyInfos())
            && !StrategyOfBookingInitUtil.hotelCheckAvail(requestType.getStrategyInfos()) &&
            (!QConfigOfCustomConfig.isSupport("SWITCH_INTEGRATION_BOOKING_INIT", Optional.ofNullable(requestType.getIntegrationSoaRequestType())
            .map(IntegrationSoaRequestType::getUserInfo)
            .map(UserInfo::getCorpId).orElse("")) || StrategyOfBookingInitUtil.onlyQueryDefaultApproveInfo(requestType.getStrategyInfos()))) {
            return bookInitResponseType;
        }
        ApprovalInput approvalInput = buildApprovalInput(requestType, approvalOutput);
        // todo:切换后，部分接口可以往上提 ---------------新逻辑------------------------->
        WaitFuture<QueryOrderSettingsRequestType, QueryOrderSettingsResponseType> queryOrderSettingsResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needQueryHotelOrderData(orderId)) {
            queryOrderSettingsResponseTypeWaitFuture =
                handlerOfQueryOrderSettings.handleAsync(mapperOfQueryOrderSettingsRequestType.map(Tuple1.of(orderId)));
        }
        // 城市信息
        WaitFuture<GetCityBaseInfoRequestType, GetCityBaseInfoResponseType> getCityBaseInfoResponseTypeWaitFuture = null;
        if (StrategyOfBookingInitUtil.needCityBaseInfo(requestType.getStrategyInfos())) {
            getCityBaseInfoResponseTypeWaitFuture = handlerOfGetCityBaseInfo.handleAsync(
                    mapperOfGetCityBaseInfoRequestType.map(Tuple2.of(requestType.getIntegrationSoaRequestType(),
                            getCityInput(queryHotelOrderDataResponseType, resourceToken))));
        }
        // 行程信息
        WaitFuture<GetTripBookingInfosRequestType, GetTripBookingInfosResponseType> getTripBookingInfosResponseWaitFuture = null;
        WaitFuture<SearchTripDetailRequestType, SearchTripDetailResponseType>
            searchTripDetailResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetTripBookingInfos(requestType)) {
            getTripBookingInfosResponseWaitFuture = handlerOfGetTripBookingInfos.handleAsync(
                mapperOfGetTripBookingInfosRequest.map(
                    Tuple2.of(BookingInitUtil.buildTripInput(requestType.getTripInfoInput()), resourceToken)));
            searchTripDetailResponseTypeWaitFuture = handlerOfSearchTripDetail.handleAsync(
                mapperOfSearchTripDetailRequestType.map(Tuple3.of(requestType.getIntegrationSoaRequestType(),
                    requestType.getTripInfoInput().getTripId(), null)));
        }
        WaitFuture<BatchSearchClientsInfoRequestType, BatchSearchClientsInfoResponseType> batchSearchClientsInfoResponseTypeWaitFuture =
            handlerOfBatchSearchClientsInfo.handleAsync(mapperOfBatchSearchClientsInfoRequestType.map(Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // 酒店详情信息
        WaitFuture<GetHotelDetailInfoRequestType, GetHotelDetailInfoResponseType> getHotelDetailInfoWaitFuture =
            handlerOfGetHotelDetailInfo.handleAsync(mapperOfGetHotelDetailInfoRequest.map(Tuple2.of(requestType, resourceToken)));
        // 发票信息
        WaitFuture<GetContactInvoiceDefaultInfoRequestType, GetContactInvoiceDefaultInfoResponseType> getContactInvoiceDefaultInfoResponseTypeWaitFuture =
            handlerOfGetContactInvoiceDefaultInfo.handleAsync(mapperOfGetContactInvoiceDefaultInfoRequest.map(Tuple1.of(requestType)));
        WaitFuture<GetInvoiceTitlesRequest, GetInvoiceTitlesResponse> getInvoiceTitlesResponseWaitFuture = handlerOfGetInvoiceTitleList.handleAsync(
            mapperOfGetInvoiceTitlesRequest.map(Tuple1.of(requestType)));
        WaitFuture<GetUserAddressInfoRequestType, GetUserAddressInfoResponseType> getUserAddressInfoResponseTypeWaitFuture =
            handlerOfGetUserAddressInfo.handleAsync(mapperOfGetUserAddressInfoRequestType.map(Tuple1.of(requestType)));
        // 获取主营卡uid
        WaitFuture<QueryBizModeBindRelationRequestType, QueryBizModeBindRelationResponseType> queryBizModeBindRelationResponseTypeWaitFuture = null;
        QueryBizModeBindRelationRequestType queryBizModeBindRelationRequestType =
                mapperOfQueryBizModeBindRelationRequestType.map(Tuple2.of(requestType.getIntegrationSoaRequestType(), null));
        if (queryBizModeBindRelationRequestType != null) {
            queryBizModeBindRelationResponseTypeWaitFuture = handlerOfQueryBizModeBindRelation.handleAsync(queryBizModeBindRelationRequestType);
        }
        // 散客uid,获取会员绑定
        WaitFuture<GetPlatformRelationByUidRequestType, GetPlatformRelationByUidResponseType> getPlatformRelationByUidResponseTypeWaitFuture = null;
        GetPlatformRelationByUidRequestType getPlatformRelationByUidRequestType =
                mapperOfGetPlatformRelationByUidRequestType.map(Tuple2.of(requestType.getIntegrationSoaRequestType(),
                WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture)));
        if (getPlatformRelationByUidRequestType != null) {
            getPlatformRelationByUidResponseTypeWaitFuture = handlerOfGetPlatformRelationByUid.handleAsync(getPlatformRelationByUidRequestType);
        }
        // 审批沿用需要用到的开关
        WaitFuture<GetAuthDelayRequestType, GetAuthDelayResponseType> getAuthDelayResponseTypeWaitFuture =
            handlerOfGetAuthDelayConfig.handleAsync(mapperOfGetAuthDelayRequestType.map(Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // ip属地
        WaitFuture<GetIpInfoRequestTypeV2, GetIpInfoResponseTypeV2> getIpInfoWaitFuture =
            handlerOfGetIpInfo.handleAsync(mapperOfGetIpInfoRequest.map(Tuple1.of(requestType.getIntegrationSoaRequestType())));
        // 查询差标
        WaitFuture<GetHotelTravelPolicyRequestType, GetHotelTravelPolicyResponseType> getHotelTravelPolicyResponseTypeWaitFuture = null;
        // 支付方式计算
        WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo hotelTravelPolicyInfo = null;
        if (BookingInitProcessorOfUtil.needGetHotelTravelPolicy(requestType, accountInfo)) {
            GetTravelPolicyRequest getTravelPolicyRequest = mapperOfBuildTravelPolicyRequestType.map(
                Tuple5.of(requestType, resourceToken, approvalInput, selectedRoomPayType, accountInfo));
            getHotelTravelPolicyResponseTypeWaitFuture = handlerOfGetHotelTravelPolicy.handleAsync(mapperOfGetHotelTravelPolicyRequest
                .map(Tuple2.of(getTravelPolicyRequest, accountInfo)));
            // ============================== level4 ==============================
            GetHotelTravelPolicyResponseType getHotelTravelPolicyResponseType = WaitFutureUtil.safeGetFuture(getHotelTravelPolicyResponseTypeWaitFuture);
            if (getHotelTravelPolicyResponseType != null) {
                hotelTravelPolicyInfo = WrapperOfHotelTravelPolicy.builder().
                    hotelTravelPolicyResponseType(getHotelTravelPolicyResponseType).build();
            }
        }
        // 查询可订
        // ============================== level5 ==============================
        WaitFuture<CheckAvailRequestType, CheckAvailResponseType> checkAvailResponseTypeWaitFuture =
            handlerOfCheckAvail.handleAsync(mapperOfCheckAvailRequest.map(Tuple5.of(requestType, hotelTravelPolicyInfo, accountInfo,
                resourceToken, getIpInfoWaitFuture.getWithoutError())));
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
            .setCheckAvailResponseType(WaitFutureUtil.safeGetFuture(checkAvailResponseTypeWaitFuture))
            .setResourceToken(resourceToken)
            .check(qConfigOfCodeMappingConfig, requestType.getStrategyInfos()).build().getCheckAvailInfo();
        WaitFuture<SearchRegistrationFieldsRequestType, SearchRegistrationFieldsResponseType> searchRegistrationFieldsResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needSearchRegistrationFields(checkAvailInfo)) {
            searchRegistrationFieldsResponseTypeWaitFuture = handlerOfSearchRegistrationFields.handleAsync(
                mapperOfSearchRegistrationFieldsRequestType.map(Tuple2.of(requestType.getIntegrationSoaRequestType(), checkAvailInfo)));
        }
        // 优惠券信息  handlerOfGetMultilingualCouponInfo.handleAsync(mapperOfGetMultilingualCouponInfoRequestType.map(Tuple2.of(requestType, checkAvailInfo)));
        WaitFuture<GetMultilingualCouponInfoRequestType, GetMultilingualCouponInfoResponseType> getMultilingualCouponInfoResponseTypeWaitFuture = null;
        // 集团会员信息
        WaitFuture<QueryCtripMrgMemberUserInfoRequest, CtripMemberUserInfoResponse> ctripMemberUserInfoResponseWaitFuture = null;
        if (BookingInitProcessorOfUtil.needQueryMrgMemberUserInfo(checkAvailInfo)) {
            ctripMemberUserInfoResponseWaitFuture =
                handlerOfQueryMrgMemberUserInfo.handleAsync(mapperOfQueryCtripMrgMemberUserInfoRequest.map(Tuple4.of(requestType, checkAvailInfo,
                    WaitFutureUtil.safeGetFutureWithoutError(getPlatformRelationByUidResponseTypeWaitFuture),
                        WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture))));
        }
        // 查询用户酒店会员卡信息
        WaitFuture<GetCorpUserHotelVipCardRequestType, GetCorpUserHotelVipCardResponseType> getCorpUserHotelVipCardResponseTypeWaitFuture = null;
        GetCorpUserHotelVipCardRequestType getCorpUserHotelVipCardRequestType = mapperOfGetCorpUserHotelVipCardRequestType.map(
                Tuple5.of(requestType.getIntegrationSoaRequestType(), checkAvailInfo,
                WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture),
                        requestType.getStrategyInfos(), requestType.getHotelBookPassengerInputs()));
        if (getCorpUserHotelVipCardRequestType != null) {
            getCorpUserHotelVipCardResponseTypeWaitFuture = handlerOfGetCorpUserHotelVipCard.handleAsync(getCorpUserHotelVipCardRequestType);
        }

        QueryOrderSettingsResponseType queryOrderSettingsResponseType = WaitFutureUtil.safeGetFutureWithoutError(queryOrderSettingsResponseTypeWaitFuture);
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType = null;
        // 支付方式
        WaitFuture<GetSupportedPaymentMethodRequestType, GetSupportedPaymentMethodResponseType>
            getSupportedPaymentMethodResponseTypeWaitFuture = handlerOfGetSupportedPaymentMethod.handleAsync(
            mapperOfGetSupportedPaymentMethodRequestType.map(
                Tuple6.of(requestType, checkAvailInfo, hotelTravelPolicyInfo, accountInfo,
                    queryHotelOrderDataResponseType, queryOrderSettingsResponseType)));
        if (BookingInitProcessorOfUtil.needCalculateServiceChargeV2(requestType)) {
            // 服务费V2接口
            WaitFuture<CalculateServiceChargeV2RequestType, CalculateServiceChargeV2ResponseType>
                calculateServiceChargeV2ResponseTypeWaitFuture = handlerOfCalculateServiceChargeV2.handleAsync(
                mapperOfCalculateServiceChargeV2Request.map(
                    Tuple7.of(accountInfo, queryHotelOrderDataResponseType, queryOrderSettingsResponseType,
                        checkAvailInfo, requestType, resourceToken,
                        getSupportedPaymentMethodResponseTypeWaitFuture.getWithoutError())));
            // ============================== level6 ==============================
            calculateServiceChargeV2ResponseType = calculateServiceChargeV2ResponseTypeWaitFuture.getWithoutError();
        }
        // 发票信息
        ReimbursementQueryRequestType reimbursementQueryRequest = mapperOfReimbursementQueryRequestType.map(Tuple2.of(requestType, queryHotelOrderDataRequestType));
        WaitFuture<ReimbursementQueryRequestType, ReimbursementQueryResponseType> reimbursementQueryResponseTypeWaitFuture = null;
        if (reimbursementQueryRequest != null) {
            reimbursementQueryResponseTypeWaitFuture = handlerOfReimbursementQuery.handleAsync(reimbursementQueryRequest);
        }

        // ============================== level7 ==============================
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType = getSupportedPaymentMethodResponseTypeWaitFuture.getWithoutError();
        checkGetSupportedPaymentMethodResponseType(getSupportedPaymentMethodResponseType);
        HotelPayTypeEnum servicePayTypeEnum = BookingInitUtil.getServicePayType(requestType.getHotelPayTypeInput(),selectedRoomPayType, getSupportedPaymentMethodResponseType, null,
            accountInfo, calculateServiceChargeV2ResponseType);
        HotelPayTypeEnum roomPayTypeEnum =
            BookingInitUtil.getRoomPayType(selectedRoomPayType, getSupportedPaymentMethodResponseType, requestType.getFlashStayInput(),
                accountInfo);
        WaitFuture<GetSupportedInvoiceTypeRequestType, GetSupportedInvoiceTypeResponseType>
            getSupportedInvoiceTypeResponseTypeWaitFuture = null;
        if (!BookingInitUtil.supportNewInvoice(requestType)) {
            getSupportedInvoiceTypeResponseTypeWaitFuture = handlerOfGetSupportedInvoiceType.handleAsync(
                mapperOfGetSupportedInvoiceRequestType.map(
                    Tuple5.of(checkAvailInfo, calculateServiceChargeV2ResponseType, requestType, roomPayTypeEnum,
                        servicePayTypeEnum)));
        }

        // 发票信息获取
        WaitFuture<CorpOrderInvoiceDetailInfoQueryRequest, CorpOrderInvoiceDetailInfoQueryResponse>
            corpOrderInvoiceDetailInfoQueryResponseWaitFuture = null;
        if (BookingInitProcessorOfUtil.needCorpOrderInvoiceInfo(strategyInfoMap)) {
            corpOrderInvoiceDetailInfoQueryResponseWaitFuture = handlerOfCorpOrderInvoiceInfo.handleAsync(
                mapperOfCorpOrderInvoiceDetailInfoQueryRequest.map(
                    Tuple6.of(requestType, resourceToken, accountInfo, checkAvailInfo, roomPayTypeEnum,
                        servicePayTypeEnum)));
        }

        // 旅行奖励费用计算
        WaitFuture<CalculateTravelRewardsRequestType, CalculateTravelRewardsResponseType> calculateTravelRewardsResponseTypeWaitFuture =
            handlerOfCalculateTravelRewards.handleAsync(mapperOfCalculateTravelRewardsRequest.map(Tuple7.of(requestType,
                checkAvailInfo, hotelTravelPolicyInfo,
                calculateServiceChargeV2ResponseType, accountInfo, getSupportedPaymentMethodResponseType, selectedRoomPayType)));
        // agg费用详情
        WaitFuture<DistributePaymentAmountRequestType, DistributePaymentAmountResponseType> distributePaymentAmountResponseTypeWaitFuture =
            handlerOfDistributePaymentAmount.handleAsync(mapperOfDistributePaymentAmountRequest.map(Tuple7.of(requestType, checkAvailInfo,
                hotelTravelPolicyInfo, getSupportedPaymentMethodResponseType, accountInfo, calculateServiceChargeV2ResponseType, selectedRoomPayType)));
        // 取消政策
        WaitFuture<GetCancelPolicyDescRequestType, GetCancelPolicyDescResponseType> getCancelPolicyDescResponseTypeWaitFuture =
            handlerOfGetCancelPolicyDesc.handleAsync(mapperOfGetCancelPolicyDescRequest.map(Tuple5.of(requestType, checkAvailInfo,
                calculateServiceChargeV2ResponseType, roomPayTypeEnum, servicePayTypeEnum)));
        // 服务费政策
        WaitFuture<GetRoomChargePolicyListRequestType, GetRoomChargePolicyListResponseType> getRoomChargePolicyListWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetRoomChargePolicyList(requestType.getCorpPayInfo())) {
            getRoomChargePolicyListWaitFuture =
                handlerOfGetRoomChargePolicyList.handleAsync(mapperOfGetRoomChargePolicyListRequestType.map(Tuple8.of(accountInfo,
                    queryHotelOrderDataResponseType,
                    WaitFutureUtil.safeGetFutureWithoutError(queryOrderSettingsResponseTypeWaitFuture),
                    checkAvailInfo,
                    requestType,
                    resourceToken,
                    getSupportedPaymentMethodResponseType,
                    roomPayTypeEnum)));
        }

        GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType = WaitFutureUtil.safeGetFutureWithoutError(getPlatformRelationByUidResponseTypeWaitFuture);
        // 集团信息
        WaitFuture<GetGroupVipPackageRequestType, GetGroupVipPackageResponseType> getGroupVipPackageResponseTypeWaitFuture =
            handlerOfGetGroupVipPackage.handleAsync(mapperOfGetGroupVipPackageRequestType.map(Tuple3.of(requestType,
                checkAvailInfo, getPlatformRelationByUidResponseType)));
        GetCancelPolicyDescResponseType getCancelPolicyDescResponse = getCancelPolicyDescResponseTypeWaitFuture.getWithoutError();
        CancelPolicyDescType cancelPolicyDescType = Optional.ofNullable(getCancelPolicyDescResponse)
            .map(GetCancelPolicyDescResponseType::getCancelPolicyDescList).filter(CollectionUtil::isNotEmpty).map(o -> o.get(0)).orElse(null);
        ReimbursementDetailInfoType reimbursementDetailInfoType = Optional.ofNullable(reimbursementQueryResponseTypeWaitFuture)
            .map(x -> x.getWithoutError())
            .map(ReimbursementQueryResponseType::getReimbursementInfo)
            .map(ReimbursementInfoType::getReimbursementDetailInfoList)
            .map(o -> o.get(0))
            .orElse(null);
        GetPackageRoomListResponseType getPackageRoomListResponseType = null;
        if (BookingInitProcessorOfUtil.needGetPackageRoomList(checkAvailInfo)) {
            WaitFuture<GetPackageRoomListRequestType, GetPackageRoomListResponseType> getPackageRoomListWaitFuture =
                handlerOfGetPackageRoomList.handleAsync(mapperOfGetPackageRoomListRequestType.map(Tuple3.of(requestType, resourceToken, checkAvailInfo)));
            getPackageRoomListResponseType = getPackageRoomListWaitFuture.getWithoutError();
        }
        GetPackageRoomSnapshotResponseType getPackageRoomSnapshotResponseType = null;
        if (BookingInitProcessorOfUtil.needGetPackageRoomSnapshot(resourceToken, checkAvailInfo)) {
            WaitFuture<GetPackageRoomSnapshotRequestType, GetPackageRoomSnapshotResponseType> GetPackageRoomSnapshotWaitFuture =
                handlerOfGetPackageRoomSnapshot.handleAsync(mapperOfGetPackageRoomSnapshotRequestType.map(Tuple3.of(requestType, resourceToken, checkAvailInfo)));
            getPackageRoomSnapshotResponseType = GetPackageRoomSnapshotWaitFuture.getWithoutError();
        }
        WaitFuture<XProductEnquireRequestType, XProductEnquireResponseType> xProductEnquireResponseTypeWaitFuture =
            null;
        if (BookingInitProcessorOfUtil.needXProductEnquire(queryHotelOrderDataResponseType)) {
            xProductEnquireResponseTypeWaitFuture = handlerOfXProductEnquire.handleAsync(
                mapperOfXProductEnquireRequestType.map(Tuple2.of(requestType,
                    mapperOfQueryHotelOrderDataRequest.map(
                        Tuple2.of(requestType.getIntegrationSoaRequestType(), orderId)))));
        }
        // 国籍限制信息
        WaitFuture<CountryQueryRequestType, CountryQueryResponseType> countryQueryResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needCountryQuery(requestType.getStrategyInfos())) {
            CountryQueryRequestType countryQueryRequestType = mapperOfCountryQueryRequestType.map(Tuple2.of(requestType.getIntegrationSoaRequestType(),
                    Optional.ofNullable(checkAvailInfo).map(WrapperOfCheckAvail.CheckAvailInfo::getBookingRules)
                            .map(BookingRulesType::getNationalityRestrictionInfo).orElse(null)));
            if (countryQueryRequestType != null) {
                countryQueryResponseTypeWaitFuture = handlerOfCountryQuery.handleAsync(countryQueryRequestType);
            }
        }
        // 联系人国家码限制信息
        WaitFuture<GetAllNationalityRequestType, GetAllNationalityResponseType> getAllNationalityResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needGetAllNationality(requestType.getStrategyInfos())) {
            GetAllNationalityRequestType getAllNationalityRequestType = mapperOfGetAllNationalityRequestType.map(
                    Tuple1.of(requestType.getIntegrationSoaRequestType()));
            if (getAllNationalityRequestType != null) {
                getAllNationalityResponseTypeWaitFuture = handlerOfGetAllNationality.handleAsync(getAllNationalityRequestType);
            }
        }

        // 查询CheckTravelPolicy 和 审批单信息
        WaitFuture<CheckTravelPolicyRequestType, CheckTravelPolicyResponseType> checkTravelPolicyResponseTypeWaitFuture = null;
        if (BookingInitProcessorOfUtil.needCheckTravelPolicy(requestType)) {
            WrapperOfCheckTravelPolicy checkTravelPolicy = WrapperOfCheckTravelPolicy.builder()
                .setResourceToken(resourceToken)
                .setCheckAvailInfo(checkAvailInfo)
                .setAccountInfo(accountInfo)
                .setHotelPolicyInput(new HotelPolicyInput(requestType.getPolicyInput(), null))
                .setIntegrationSoaRequestType(requestType.getIntegrationSoaRequestType())
                .setApprovalInput(approvalInput)
                .setAddPriceInput(requestType.getAddPriceInput())
                .setHotelBookInput(requestType.getHotelBookInput())
                .setScene(MapperOfCheckTravelPolicyRequestType.SCENE_PAY_TYPE_FROM_DEFAULT )
                .setHotelInsuranceInput(requestType.getHotelInsuranceInput())
                .setHotelPayTypeInputs(requestType.getHotelPayTypeInput())
                .setGetSupportedPaymentMethodResponseType(getSupportedPaymentMethodResponseType)
                .setFlashStayInput(requestType.getFlashStayInput())
                .setCalculateServiceChargeV2ResponseType(calculateServiceChargeV2ResponseType)
                .setRcInfos(requestType.getRcInfos())
                .setHotelBookPassengerInputs(requestType.getHotelBookPassengerInputs())
                .setCorpPayInfo(requestType.getCorpPayInfo())
                .setRoomPayType(roomPayTypeEnum)
                .setStrategyInfos(requestType.getStrategyInfos())
                .setPolicyToken(Optional.ofNullable(hotelTravelPolicyInfo).map(WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo::getPolicyToken).orElse(null))
                .setServicePayType(servicePayTypeEnum)
                .setStrategyInfoMap(strategyInfoMap)
                .build();
            CheckTravelPolicyRequestType checkTravelPolicyRequestType =
                mapperOfCheckTravelPolicyRequestType.map(Tuple1.of(checkTravelPolicy));
            checkTravelPolicyResponseTypeWaitFuture = handlerOfCheckTravelPolicy.handleAsync(checkTravelPolicyRequestType);
        }
        BookingInitAssembleRequest bookInitAssembleRequest = BookingInitAssembleRequest.builder()
            .withBookingInitRequest(requestType)
            .withCancelPolicyDesc(cancelPolicyDescType)
            .withGetAuthDelayResponse(getAuthDelayResponseTypeWaitFuture.getWithoutError())
            .withAccountInfo(accountInfo)
            .withQueryHotelOrderDataResponse(queryHotelOrderDataResponseType)
            .withGetSupportedPaymentMethodResponse(getSupportedPaymentMethodResponseType)
            .withGetGroupVipPackageResponse(getGroupVipPackageResponseTypeWaitFuture.getWithoutError())
            .withCtripMemberUserInfoResponse(Optional.ofNullable(ctripMemberUserInfoResponseWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
            .withCalculateTravelRewardsResponse(calculateTravelRewardsResponseTypeWaitFuture.getWithoutError())
            .withQConfigOfPersonalAccountConfig(qConfigOfPersonalAccountConfig.getPersonalAccountConfig())
            .withRoomNumberInfoConfig(qConfigOfRoomNumberInfoConfig.getRoomNumberInfoConfig())
            .withQueryIndividualAccountResponse(WaitFutureUtil.safeGetFuture(queryIndividualAccountWaitFuture))
            .withGetCorpUserHotelVipCardResponse(WaitFutureUtil.safeGetFutureWithoutError(getCorpUserHotelVipCardResponseTypeWaitFuture))
            .withCalculateServiceChargeV2Response(calculateServiceChargeV2ResponseType)
            .withReimbursementDetailInfoType(reimbursementDetailInfoType)
            .withGetUserAddressInfoResponseType(getUserAddressInfoResponseTypeWaitFuture.getWithoutError())
            .withGetSupportedInvoiceTypeResponseType(WaitFutureUtil.safeGetFuture(getSupportedInvoiceTypeResponseTypeWaitFuture))
            .withGetContactInvoiceDefaultInfoResponseType(getContactInvoiceDefaultInfoResponseTypeWaitFuture.getWithoutError())
            .withGetInvoiceTitlesResponse(getInvoiceTitlesResponseWaitFuture.getWithoutError())
            .withRoomPayType(roomPayTypeEnum)
            .withServicePayType(servicePayTypeEnum)
            .withResourceToken(resourceToken)
            .withGetCorpUserInfoResponseType(getCorpUserInfoResponseType)
            .withHotelTravelPolicyInfo(hotelTravelPolicyInfo)
            .withCheckAvailInfo(checkAvailInfo)
            .withApprovalInfo(approvalInfo)
            .withGetCorpUserInfoByPolicyResponseType(WaitFutureUtil.safeGetFuture(getCorpUserInfoByPolicyResponseTypeWaitFuture))
            .withDistributePaymentAmountResponse(distributePaymentAmountResponseTypeWaitFuture.get())
            .withGetMultilingualCouponInfoResponseType(Optional.ofNullable(getMultilingualCouponInfoResponseTypeWaitFuture)
                .map(WaitFuture::getWithoutError).orElse(null))
            .withApprovalDefaultResponseType(WaitFutureUtil.safeGetFuture(batchApprovalDefaultResponseTypeWaitFuture))
            .withApprovalTextInfoResponseType(WaitFutureUtil.safeGetFuture(approvalTextInfoResponseTypeWaitFuture))
            .withGetPackageRoomListResponseType(getPackageRoomListResponseType)
            .withGetPackageRoomSnapshotResponseType(getPackageRoomSnapshotResponseType)
            .withGetHotelDetailInfoResponseType(getHotelDetailInfoWaitFuture.getWithoutError())
            .withSearchRegistrationFieldsResponseType(WaitFutureUtil.safeGetFutureWithoutError(searchRegistrationFieldsResponseTypeWaitFuture))
            .withBatchSearchClientsInfoResponseType(batchSearchClientsInfoResponseTypeWaitFuture.getWithoutError())
            .withQConfigOfContactConfig(qConfigOfContactConfig)
            .withCheckTravelPolicyResponseType(Optional.ofNullable(checkTravelPolicyResponseTypeWaitFuture).map(WaitFuture::get).orElse(null))
            .withXProductEnquireResponseType(WaitFutureUtil.safeGetFutureWithoutError(xProductEnquireResponseTypeWaitFuture))
            .withGetTripBookingInfosResponseType(Optional.ofNullable(getTripBookingInfosResponseWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
            .withQconfigEntityOfGroupHotelMemberCardRule(qconfigOfGroupHotelMemberCardRuleConfig.getQconfigEntityOfGroupHotelMemberCardRule())
            .withBookInitToken(bookInitToken)
            .withApprovalOutPut(approvalOutput)
            .withGetPlatformRelationByUidResponseType(getPlatformRelationByUidResponseType)
            .withSearchTripDetailResponseType(Optional.ofNullable(searchTripDetailResponseTypeWaitFuture).map(WaitFuture::getWithoutError).orElse(null))
            .withApprovalInput(approvalInput)
            .withSelectedRoomPayType(selectedRoomPayType)
            .withMemberBonusRuleEntries(QConfigUtil.getFile(QCONFIG_FILE_MEMBER_BONUS_RULE).asJsonArray(MemberBonusRuleEntry.class, null))
            .withPayConfigResponseType(WaitFutureUtil.safeGetFutureWithoutError(payConfigResponseTypeWaitFuture))
            .withGetRoomChargePolicyListResponseType(WaitFutureUtil.safeGetFutureWithoutError(getRoomChargePolicyListWaitFuture))
            .withGetReasoncodesResponseType(WaitFutureUtil.safeGetFutureWithoutError(getReasoncodesResponseTypeWaitFuture))
            .withCustomizedSharkConfigList(qConfigOfCustomizedSharkConfig.getCustomizedSharkConfigs())
            .withQueryBizModeBindRelationResponseType(WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture))
            .withCorpOrderInvoiceDetailInfoQueryResponse(
                WaitFutureUtil.safeGetFutureWithoutError(corpOrderInvoiceDetailInfoQueryResponseWaitFuture))
            .withQconfigOfRegisterConfig(qconfigOfRegisterConfig)
            .withStrategyInfoMap(strategyInfoMap)
            .withCountryQueryResponseType(WaitFutureUtil.safeGetFutureWithoutError(countryQueryResponseTypeWaitFuture))
            .withGetAllNationalityResponseType(WaitFutureUtil.safeGetFutureWithoutError(getAllNationalityResponseTypeWaitFuture))
            .withGetCityBaseInfoResponseType(WaitFutureUtil.safeGetFutureWithoutError(getCityBaseInfoResponseTypeWaitFuture))
            .withQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
            .withCostCenterNew(BookingInitUtil.buildCostCenterNew(requestType))
            .withSearchTripBasicInfoResponseTypeOfOriginalOrder(
                WaitFutureUtil.safeGetFutureWithoutError(searchTripBasicInfoResponseTypeWaitFutureOfOriginalOrder))
            .build();
        return mapperOfBookingInitResponse.map(Tuple1.of(bookInitAssembleRequest));
    }

    private ApprovalInput buildApprovalInput(BookingInitRequestType requestType, ApprovalOutput approvalOutput) {
        if (requestType == null) {
            return null;
        }
        if (!StrategyOfBookingInitUtil.firstRequest(requestType.getStrategyInfos())) {
            return requestType.getApprovalInput();
        }
        if (approvalOutput == null) {
            return null;
        }
        ApprovalInput approvalInput = new ApprovalInput();
        approvalInput.setSubApprovalNo(approvalOutput.getDefaultApprovalSubNo());
        approvalInput.setMasterApprovalNo(approvalOutput.getDefaultApprovalMainNo());
        approvalInput.setEmergency(approvalOutput.getDefaultEmergencyBook());
        return approvalInput;
    }
    @Override
    public Map<String, String> tracking(BookingInitRequestType requestType, BookingInitResponseType responseType) {
        Map<String, String> trackingMap = TrackingUtil.buildBaseTrackingMap(requestType.getHotelBookInput());
        trackingMap.put(TrackingEnum.CITY_ID.getCode(),
            Optional.ofNullable(responseType).map(BookingInitResponseType::getBookingHotelInfo)
                .map(BookingHotelInfo::getHotelPositionInfo).map(HotelPositionInfo::getCityInfo)
                .map(CityInfo::getCityId).orElse(0).toString());
        trackingMap.put(TrackingEnum.CITY_REGION.getCode(),
            Optional.ofNullable(responseType).map(BookingInitResponseType::getBookingHotelInfo)
                .map(BookingHotelInfo::getHotelPositionInfo).map(HotelPositionInfo::getCityInfo)
                .map(CityInfo::getCityArea).orElse(StringUtil.EMPTY));
        trackingMap.put("hotelId", Optional.ofNullable(responseType).map(BookingInitResponseType::getBookingHotelInfo)
            .map(BookingHotelInfo::getHotelId).orElse(StringUtil.EMPTY));
        /*todo：trackingMap.put("wsId", ""); trackingMap.put("locationId", "");
        trackingMap.put(TrackingEnum.GDS_TYPE, "");  trackingMap.put(TrackingEnum.ROOM_TYPE, StringUtil.EMPTY);*/

        if (requestType.getHotelPayTypeInput() != null) {
            HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(requestType.getHotelPayTypeInput());
            trackingMap.put("roomPayType",
                roomPayType != null ? roomPayType.getCode() : HotelPayTypeEnum.NONE.getCode());
        }
        trackingMap.put(TrackingEnum.CORP_PAY_TYPE.getCode(),
            Optional.ofNullable(requestType).map(BookingInitRequestType::getCorpPayInfo)
                .map(CorpPayInfo::getCorpPayType).orElse(StringUtil.EMPTY));

        String bookMode = TrackingBookModeEnum.NORMAL.name();
        if (responseType != null && responseType.getApprovalOutput() != null && (
            StringUtil.isNotEmpty(responseType.getApprovalOutput().getDefaultApprovalSubNo()) || "T".equalsIgnoreCase(
                responseType.getApprovalOutput().getDefaultEmergencyBook()))) {
            bookMode = TrackingBookModeEnum.APPROVAL.name();
        }
        trackingMap.put(TrackingEnum.BOOK_MODE.getCode(), bookMode);

        List<RoomProperty> roomProperties =
            Optional.ofNullable(responseType).map(BookingInitResponseType::getBookingRoomInfo)
                .map(BookingRoomInfo::getRoomProperties).orElse(null);
        Map<String, String> trackingRoomProperties = trackingForRoomProperties(roomProperties);
        if (CollectionUtil.isNotEmpty(trackingRoomProperties)) {
            for (Map.Entry<String, String> trackingRoomProperty : trackingRoomProperties.entrySet()) {
                trackingMap.put(trackingRoomProperty.getKey(), trackingRoomProperty.getValue());
            }
        }

        return trackingMap;
    }

    private Map<String, String> trackingForRoomProperties(List<RoomProperty> roomProperties) {
        if (CollectionUtil.isEmpty(roomProperties)) {
            return null;
        }

        Map<String, List<RoomProperty>> certificateRoomPropertiesMap = mapCertificateRoomProperties(roomProperties);
        if (null == certificateRoomPropertiesMap || certificateRoomPropertiesMap.isEmpty()) {
            return null;
        }

        return certificateRoomPropertiesMap.entrySet().stream().filter(Objects::nonNull)
            .collect(Collectors.toMap(Map.Entry::getKey,
            certificateRoomProperties -> roomPropertyToString(certificateRoomProperties.getValue()),
            (key1, key2) -> key1));
    }

    private Map<String, List<RoomProperty>> mapCertificateRoomProperties(List<RoomProperty> roomProperties) {
        if (CollectionUtil.isEmpty(roomProperties)) {
            return null;
        }

        return roomProperties.stream().filter(Objects::nonNull)
            .filter(roomProperty -> StringUtil.isNotBlank(roomProperty.getPropertyType()))
            .collect(Collectors.groupingBy(RoomProperty::getPropertyType));
    }

    private String roomPropertyToString(List<RoomProperty> roomProperties) {
        if (CollectionUtil.isEmpty(roomProperties)) {
            return "";
        }
        return roomProperties.stream().filter(Objects::nonNull).map(RoomProperty::getPropertyValue)
            .collect(Collectors.joining(ROOM_PROPERTY_SEPARATOR));
    }

    /**
     * 获取订单号
     *
     * @param bookingInitRequestType
     * @return
     */
    public Long getOrderId(BookingInitRequestType bookingInitRequestType, ResourceToken resourceToken) {
        Long orderId = Optional.ofNullable(resourceToken).map(ResourceToken::getOrderResourceToken)
            .map(OrderResourceToken::getOrderId).orElse(null);
        if (orderId != null) {
            return orderId;
        }
        if (!StringUtils.isEmpty(bookingInitRequestType.getOrderId())) {
            return Long.valueOf(bookingInitRequestType.getOrderId());
        }
        if (StringUtil.isNotBlank(Optional.ofNullable(bookingInitRequestType.getOriginalOrderInput())
            .map(OriginalOrderInput::getOriginalOrderId).orElse(null))) {
            return TemplateNumberUtil.parseLong(Optional.ofNullable(bookingInitRequestType.getOriginalOrderInput())
                .map(OriginalOrderInput::getOriginalOrderId).orElse(null));
        }
        return null;
    }

    /**
     * 审批单详情查询
     * 前端传入>原单
     * @param bookingInitRequestType
     * @param queryHotelOrderDataResponseType
     * @return
     */
    private SearchApprovalRequest buildSearchApprovalRequest(BookingInitRequestType bookingInitRequestType,
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType, ResourceToken resourceToken,
        WrapperOfAccount.AccountInfo accountInfo) {
        ApprovalInput approvalInput = new ApprovalInput();
        String mainApprovalNo = getMainApprovalNo(queryHotelOrderDataResponseType);
        String subApprovalNo = getSubApprovalNo(queryHotelOrderDataResponseType);
        String mainApprovalNoRequest = null;
        String subApprovalNoRequest = null;
        // 非提前审批不需要查询单据---应对单点场景，前端传入的审批单号是关联行程号，但是酒店压根没开提前审批的场景
        if (accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(
            Optional.ofNullable(resourceToken).map(ResourceToken::getHotelResourceToken)
                .map(HotelResourceToken::getHotelGeoInfoResourceToken).map(HotelGeoInfoResourceToken::getCityId)
                .orElse(null)), bookingInitRequestType.getCorpPayInfo())) {
            mainApprovalNoRequest =
                Optional.ofNullable(bookingInitRequestType.getApprovalInput()).map(ApprovalInput::getMasterApprovalNo)
                    .orElse(null);
            subApprovalNoRequest =
                Optional.ofNullable(bookingInitRequestType.getApprovalInput()).map(ApprovalInput::getSubApprovalNo)
                    .orElse(null);
        }
        if (StringUtils.isEmpty(subApprovalNo) && StringUtil.isBlank(subApprovalNoRequest)) {
            return null;
        }
        String useMainApprovalNo =
            StringUtil.isNotBlank(mainApprovalNoRequest) ? mainApprovalNoRequest : mainApprovalNo;
        String useSubApprovalNo = StringUtil.isNotBlank(subApprovalNoRequest) ? subApprovalNoRequest : subApprovalNo;
        approvalInput.setMasterApprovalNo(useMainApprovalNo);
        approvalInput.setSubApprovalNo(useSubApprovalNo);
        return SearchApprovalRequest.builder()
            .approvalInput(approvalInput)
            .cityInput(getCityInput(queryHotelOrderDataResponseType, resourceToken))
            .integrationSoaRequestType(bookingInitRequestType.getIntegrationSoaRequestType())
            .returnRegionControlCityInfo(true)
            .compatibleCitySplit(false)
            .build();
    }

    private String getSubApprovalNo(QueryHotelOrderDataResponseType queryHotelOrderDataResponseType) {
        return Optional.ofNullable(queryHotelOrderDataResponseType).map(QueryHotelOrderDataResponseType::getTravelInfo)
            .map(TravelInfoType::getApproval)
            .map(ApprovalType::getSubPreApprovalNo).orElse(null);
    }

    private String getMainApprovalNo(QueryHotelOrderDataResponseType queryHotelOrderDataResponseType) {
        return Optional.ofNullable(queryHotelOrderDataResponseType).map(QueryHotelOrderDataResponseType::getTravelInfo)
            .map(TravelInfoType::getApproval)
            .map(ApprovalType::getPrepareApprovalNo).orElse(null);
    }

    private CityInput getCityInput(QueryHotelOrderDataResponseType queryHotelOrderDataResponseType,
        ResourceToken resourceToken) {
        if (TemplateNumberUtil.isNotZeroAndNull(
            Optional.ofNullable(resourceToken).map(ResourceToken::getHotelResourceToken)
                .map(HotelResourceToken::getHotelGeoInfoResourceToken).map(HotelGeoInfoResourceToken::getCityId)
                .orElse(null))) {
            CityInput cityInput = new CityInput();
            cityInput.setCityId(resourceToken.getHotelResourceToken().getHotelGeoInfoResourceToken().getCityId());
            return cityInput;
        }
        Integer cityId = Optional.ofNullable(queryHotelOrderDataResponseType)
            .map(QueryHotelOrderDataResponseType::getHotelInfo).map(HotelInfoType::getHotelProduct)
            .map(HotelProductType::getHotel).map(HotelType::getCityId).orElse(-1);
        CityInput cityInput = new CityInput();
        cityInput.setCityId(cityId);
        return cityInput;
    }

    private void checkGetSupportedPaymentMethodResponseType(
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType) {
        if (getSupportedPaymentMethodResponseType != null && TemplateNumberUtil.equals(
            getSupportedPaymentMethodResponseType.getResponseCode(), CommonConstant.SUCCESS_20000)) {
            return;
        }
        Integer logErrorCode = Optional.ofNullable(getSupportedPaymentMethodResponseType)
            .map(GetSupportedPaymentMethodResponseType::getResponseCode)
            .orElse(BookingInitErrorEnum.ACTION_NAME_GET_SUPPORTED_PAYMENT_METHOD.getErrorCode());
        String friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
            SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_HOTEL_BOOK_COMMON_WS,
            SoaErrorSharkKeyConstant.ACTION_NAME_GET_SUPPORTED_PAYMENT_METHOD, String.valueOf(logErrorCode));
        if (StringUtil.isBlank(friendlyMessage)) {
            friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessageDefault(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_HOTEL_BOOK_COMMON_WS,
                SoaErrorSharkKeyConstant.ACTION_NAME_GET_SUPPORTED_PAYMENT_METHOD);
        }
        throw BusinessExceptionBuilder.createAlertException(
            BookingInitErrorEnum.ACTION_NAME_GET_SUPPORTED_PAYMENT_METHOD.getErrorCode(),
            Optional.ofNullable(getSupportedPaymentMethodResponseType)
                .map(GetSupportedPaymentMethodResponseType::getResponseDesc).orElse(null), friendlyMessage,
            logErrorCode.toString());
    }

}
