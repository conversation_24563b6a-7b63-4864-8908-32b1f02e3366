package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.corp.order.service.orderfoundationcentercostinfoservice.SaveOrderCostCenterResponseType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.BillAndDockingMergedResultType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckResultExtType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyApprovalBillResultType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyPassengerResultType;
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowResponseType;
import com.ctrip.corp.bff.framework.hotel.common.util.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowApprovalResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.SaveCostCenterInputItem;
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.SaveCostCenterInputVo;
import com.ctrip.corp.bff.framework.specific.common.utils.SaveCommonDataCostCenterInfoTypeUtil;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.ObjectUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.PosEnum;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AttachmentInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CostCenterInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.MiceInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.SSOInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.DistinguishReservationEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfSaveCommonData;
import com.ctrip.corp.bff.hotel.book.contract.CostCenterInfo;
import com.ctrip.corp.bff.hotel.book.contract.FollowApprovalInfoInput;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.ReservationInfo;
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCodeMappingConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.profile.contract.SSOBaseInfo;
import com.ctrip.corp.bff.profile.contract.SSODingInfo;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowComputeResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.framework.foundation.Foundation;
import com.ctrip.soa._20183.AttachmentInfoType;
import com.ctrip.soa._20183.CorpDockingInfoType;
import com.ctrip.soa._20183.CostCenterExtendInfoType;
import com.ctrip.soa._20183.CostCenterInfoType;
import com.ctrip.soa._20183.CostCenterItemInfoType;
import com.ctrip.soa._20183.CustomFieldInfoType;
import com.ctrip.soa._20183.ExtendInfoType;
import com.ctrip.soa._20183.MiceInfoType;
import com.ctrip.soa._20183.PassengerCostCenterInfoType;
import com.ctrip.soa._20183.PrepareApprovalInfoType;
import com.ctrip.soa._20183.ReservationInfoType;
import com.ctrip.soa._20183.SaveCommonDataRequestType;
import com.ctrip.soa._20183.TravelControlPreApprovalType;
import com.ctrip.soa._20183.TripInfoType;
import com.ctrip.soa._21234.ApprovalInfo;
import com.ctrip.soa._21234.CreateTripResponseType;
import com.ctrip.soa._21234.SearchTripDetailResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2024/6/23 18:31
 */
@Component public class MapperOfSaveCommonDataRequestType
    extends AbstractMapper<Tuple1<WrapperOfSaveCommonData>, SaveCommonDataRequestType> {
    private static final String ROOM_TYPE_M = "M";
    private static final String ROOM_TYPE_C = "C";
    private static final String TMC_PRICE_TYPE_NONE = "NONE";

    private static final String DOCKING_SOURCE_DINGTALK = "DingTalk";
    private static final String ATTACHMENT_TYPE_APPROVAL = "Approval";
    private static final String PRE_APPROVAL_TYPE_POLICY = "Policy";

    /**
     * 提交审批流提前审批单状态：管控通过
     */
    public static final int COMMIT_PRE_APPROVAL_NO_PASS = 1;
    /**
     * 提交审批流提前审批单状态：管控不通过
     */
    public static final int COMMIT_PRE_APPROVAL_NO_FAIL = 2;
    /**
     * 提交审批流提前审批单状态：紧急预订
     */
    public static final int COMMIT_PRE_APPROVAL_NO_URGENT = 3;
    /**
     * 提交审批流提前审批单状态：无需校验
     */
    public static final int COMMIT_PRE_APPROVAL_NO_NOTCHECK = 4;

    public static final String EMERGENCY = "EMERGENCY";
    public static final String FAIL = "FAIL";
    public static final String PASS = "PASS";
    private static final List<SourceFrom> OFFLINE_ONLINE = Arrays.asList(SourceFrom.Online, SourceFrom.Offline);

    @Override protected SaveCommonDataRequestType convert(Tuple1<WrapperOfSaveCommonData> tuple) {
        SaveCommonDataRequestType result = new SaveCommonDataRequestType();
        WrapperOfSaveCommonData wrapperOfSaveCommonData = tuple.getT1();
        CreateOrderResponseType createOrderResponseType = wrapperOfSaveCommonData.getCreateOrderResponseType();
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType =
            wrapperOfSaveCommonData.getQueryCheckAvailContextResponseType();
        CheckTravelPolicyResponseType checkTravelPolicyResponseType =
            wrapperOfSaveCommonData.getCheckTravelPolicyResponseType();
        CreateTripResponseType createTripResponseType = wrapperOfSaveCommonData.getCreateTripResponseType();
        GetCorpUserInfoResponseType getCorpUserInfoResponseTypePolicy =
            wrapperOfSaveCommonData.getGetCorpUserInfoResponseTypePolicy();
        SSOInfoQueryResponseType ssoInfoQueryResponseType = wrapperOfSaveCommonData.getSsoInfoQueryResponseType();
        OrderCreateRequestType orderCreateRequestType = wrapperOfSaveCommonData.getOrderCreateRequestType();
        MatchApprovalFlowResponseType matchApprovalFlowResponseType =
            wrapperOfSaveCommonData.getMatchApprovalFlowResponseType();
        WrapperOfAccount.AccountInfo accountInfo = wrapperOfSaveCommonData.getAccountInfo();
        ApprovalFlowComputeResponseType approvalFlowComputeResponseType =
            wrapperOfSaveCommonData.getApprovalFlowComputeResponseType();
        OrderCreateToken orderCreateToken = wrapperOfSaveCommonData.getOrderCreateToken();
        SearchTripDetailResponseType searchTripDetailResponseType =
            wrapperOfSaveCommonData.getSearchTripDetailResponseType();
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo =
            wrapperOfSaveCommonData.getCheckAvailContextInfo();
        SearchTripDetailResponseType searchTripDetailResponseTypeFolow =
            wrapperOfSaveCommonData.getSearchTripDetailResponseTypeFolow();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig =
            wrapperOfSaveCommonData.getQconfigOfCertificateInitConfig();
        ResourceToken resourceToken = wrapperOfSaveCommonData.getResourceToken();
        Map<String, StrategyInfo> strategyInfoMap = wrapperOfSaveCommonData.getStrategyInfoMap();

        result.setOrderId(OrderCreateProcessorOfUtil.buildOrderId(wrapperOfSaveCommonData.getOrderCreateToken(),
            createOrderResponseType));
        result.setProductLine(getProductLine(checkAvailContextInfo));
        UserInfo userInfo = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
            .map(IntegrationSoaRequestType::getUserInfo).orElse(null);
        if (userInfo != null) {
            result.setUid(userInfo.getUserId());
            result.setCorpId(userInfo.getCorpId());
        }
        String policyUid =
            Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
                .map(PolicyInput::getPolicyUid)
                .orElse(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        result.setPolicyUid(accountInfo.getPolicyUidForOtherPolicy(policyUid,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId()));
        result.setServerFrom(Foundation.app().getAppId());
        String language = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
            .map(IntegrationSoaRequestType::getLanguage).orElse(null);
        result.setLanguage(language);
        SourceFrom sourceFrom = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
            .map(IntegrationSoaRequestType::getSourceFrom).orElse(null);
        result.setOrderSource(getOrderSource(sourceFrom));
        result.setTravelControlPreApprovalList(
            getTravelControlPreApprovals(orderCreateRequestType, checkTravelPolicyResponseType,
                queryCheckAvailContextResponseType, getCorpUserInfoResponseTypePolicy, checkAvailContextInfo,
                qconfigOfCertificateInitConfig));
        result.setMiceInfo(getMiceInfoType(orderCreateRequestType.getMiceInput()));
        result.setCorpDockingInfoList(getCorpDockingInfoList(ssoInfoQueryResponseType));
        List<AttachmentInfo> attachmentInfos = Optional.ofNullable(orderCreateRequestType.getApprovalFlowInput())
            .map(ApprovalFlowInput::getAttachmentInfos).orElse(null);
        result.setAttachmentInfoList(getAttachmentInfoList(attachmentInfos));
        result.setExtend(getExtendInfoType(approvalFlowComputeResponseType, checkTravelPolicyResponseType,
            matchApprovalFlowResponseType, orderCreateToken, orderCreateRequestType, accountInfo,
            searchTripDetailResponseTypeFolow, createTripResponseType));
        result.setReservationInfo(
            getReservationInfo(orderCreateRequestType.getReservationInfo(), orderCreateRequestType));
        // 蓝色空间成本中心 临时用Pos判断 7A接入新版后，待灰度完成，不再需要此处逻辑
        if (orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getPos() != PosEnum.CHINA) {
            result.setCostCenter(buildCostCenterInfoTypeBlueSpace(orderCreateRequestType));
        }
        if (wrapperOfSaveCommonData.isSaveCommonDataCostCenter()) {
            result.setCostCenter(buildCostCenterInfoType(wrapperOfSaveCommonData));
        }
        // 新版成本中心2.0
        if (OrderCreateProcessorOfUtil.needCostCenterNew(resourceToken, orderCreateRequestType.getCorpPayInfo(),
            accountInfo, strategyInfoMap)) {
            result.setCostCenter(SaveCommonDataCostCenterInfoTypeUtil.genCostCenterInfoType(
                Optional.ofNullable(orderCreateRequestType.getCostCenterInfoNew()).map(
                        com.ctrip.corp.bff.framework.template.entity.contract.integration.CostCenterInfo::getCostCenterInputs)
                    .orElse(null), buildJourneyNoNew(wrapperOfSaveCommonData.getApprovalTextInfoResponseType(),
                    orderCreateRequestType.getApprovalInput(), orderCreateRequestType, accountInfo),
                OrderCreateProcessorOfUtil.buildCheckCostCenterPassengers(
                    orderCreateRequestType.getHotelBookPassengerInputs(), orderCreateRequestType.getCityInput(),
                    checkAvailContextInfo, qconfigOfCertificateInitConfig)));
        }
        return result;
    }

    @Override protected ParamCheckResult check(Tuple1<WrapperOfSaveCommonData> tuple) {
        WrapperOfSaveCommonData wrapperOfSaveCommonData = tuple.getT1();
        CreateOrderResponseType createOrderResponseType = wrapperOfSaveCommonData.getCreateOrderResponseType();
        OrderCreateToken orderCreateToken = wrapperOfSaveCommonData.getOrderCreateToken();
        ResourceToken resourceToken = wrapperOfSaveCommonData.getResourceToken();
        Map<String, StrategyInfo> strategyInfoMap = wrapperOfSaveCommonData.getStrategyInfoMap();
        QConfigOfCodeMappingConfig qConfigOfCodeMappingConfig = wrapperOfSaveCommonData.getQConfigOfCodeMappingConfig();
        if (!orderCreateToken.isUseOrderCreate()) {
            return null;
        }
        boolean priceChangeOrConfirmOrder = !OrderCreateProcessorOfUtil.requireCreateOrder(orderCreateToken);
        // Integer errorCode qconfig map的结果, String logErrorCode 接口的errorCode
        if (!Optional.ofNullable(createOrderResponseType).map(CreateOrderResponseType::getResponseCode).orElse(0)
            .equals(CommonConstant.SUCCESS_20000) && !priceChangeOrConfirmOrder) {
            return OrderCreateProcessorOfUtil.buildParamCheckResultCreateOrderError(createOrderResponseType,
                qConfigOfCodeMappingConfig);
        }
        if (priceChangeOrConfirmOrder && orderCreateToken.getCreateOrderResult() == null) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.CREATE_ORDER_ERROR,
                this.getClass().getName() + " error");
        }
        if (OrderCreateProcessorOfUtil.saveOrderCostCenter(wrapperOfSaveCommonData.getOrderCreateRequestType(),
            wrapperOfSaveCommonData.getAccountInfo(), wrapperOfSaveCommonData.isSaveCommonDataCostCenter(),
            orderCreateToken, createOrderResponseType, resourceToken, strategyInfoMap)) {
            SaveOrderCostCenterResponseType saveOrderCostCenterResponseType =
                wrapperOfSaveCommonData.getSaveOrderCostCenterResponseType();
            if (saveOrderCostCenterResponseType == null || CollectionUtil.isEmpty(
                saveOrderCostCenterResponseType.getSavedList())
                || saveOrderCostCenterResponseType.getSavedList().get(0) == null || !Integer.valueOf(0)
                .equals(saveOrderCostCenterResponseType.getSavedList().get(0).getErrorCode())) {
                throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.SAVE_ORDER_COST_CENTER_ERROR,
                    "");
            }
        }
        return null;
    }

    /**
     * 对比降噪，APP/前端传入时new了对象
     * 非APP&前端未传入是个null
     * @param orderCreateRequestType
     * @return
     */
    private CostCenterInfoType buildCostCenterInfoTypeBlueSpace(OrderCreateRequestType orderCreateRequestType) {
        if (OFFLINE_ONLINE.contains(orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom())
            && CollectionUtil.isEmpty(orderCreateRequestType.getCostCenterInputs())) {
            return null;
        }
        CostCenterInfoType costCenterInfoType =
            SaveCommonDataCostCenterInfoTypeUtil.genCostCenterInfoType(orderCreateRequestType.getCostCenterInputs(),
                null, null);
        String journeyNo = Optional.ofNullable(costCenterInfoType).map(CostCenterInfoType::getCostCenterExtendInfo)
            .map(CostCenterExtendInfoType::getJourneyNo).orElse(null);
        if (StringUtil.isNotEmpty(
            Optional.ofNullable(orderCreateRequestType.getApprovalInput()).map(ApprovalInput::getSubApprovalNo)
                .orElse(null)) && StringUtil.isEmpty(journeyNo) && costCenterInfoType != null) {
            CostCenterExtendInfoType costCenterExtendInfoType =
                Optional.of(costCenterInfoType).map(CostCenterInfoType::getCostCenterExtendInfo)
                    .orElse(new CostCenterExtendInfoType());
            costCenterExtendInfoType.setJourneyNo(orderCreateRequestType.getApprovalInput().getSubApprovalNo());
            costCenterInfoType.setCostCenterExtendInfo(costCenterExtendInfoType);
        }
        return costCenterInfoType;
    }

    /**
     * 紧急预订名称获取
     *
     * @return
     */
    protected String buildEmergencyName(ApprovalTextInfoResponseType approvalTextInfoResponseType,
        ApprovalInput approvalInput, OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo) {
        if (!accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
            orderCreateRequestType.getCorpPayInfo())) {
            return null;
        }
        if (BooleanUtil.parseStr(true)
            .equalsIgnoreCase(Optional.ofNullable(approvalInput).map(ApprovalInput::getEmergency).orElse(null))
            && StringUtil.isBlank(
            Optional.ofNullable(approvalInput).map(ApprovalInput::getSubApprovalNo).orElse(null))) {
            return Optional.ofNullable(approvalTextInfoResponseType).map(ApprovalTextInfoResponseType::getEmergencyName)
                .orElse(null);
        }
        return null;
    }

    /**
     * 组装成本中心信息对象
     *
     * @return 结果
     */
    private CostCenterInfoType buildCostCenterInfoType(WrapperOfSaveCommonData wrapperOfSaveCommonData) {
        if (wrapperOfSaveCommonData.getAccountInfo().isPackageEnabled() && CorpPayInfoUtil.isPublic(
            wrapperOfSaveCommonData.getOrderCreateRequestType().getCorpPayInfo())) {
            return null;
        }
        // 传入的成本中心解析结果
        SaveCostCenterInputVo saveCostCenterInputVo = null;
        if (StringUtils.isNotBlank(Optional.ofNullable(wrapperOfSaveCommonData.getOrderCreateRequestType().getCostCenterInfo()).map(
            CostCenterInfo::getCostCenterJsonString).orElse(null))) {
            saveCostCenterInputVo = JsonUtil.fromJsonIgnoreCase(
                wrapperOfSaveCommonData.getOrderCreateRequestType().getCostCenterInfo().getCostCenterJsonString(),
                SaveCostCenterInputVo.class);
        }
        // 成本中心解析结果空
        if (null == saveCostCenterInputVo || CollectionUtil.isEmpty(saveCostCenterInputVo.getItems())) {
            if (StringUtil.isNotBlank(
                Optional.ofNullable(wrapperOfSaveCommonData.getOrderCreateRequestType().getApprovalInput())
                    .map(ApprovalInput::getSubApprovalNo).orElse(null))) {
                CostCenterInfoType costCenterInfoType = new CostCenterInfoType();
                CostCenterExtendInfoType costCenterExtendInfoType = new CostCenterExtendInfoType();
                // 成本中心的提前审批单号
                costCenterExtendInfoType.setJourneyNo(
                    wrapperOfSaveCommonData.getOrderCreateRequestType().getApprovalInput().getSubApprovalNo());
                costCenterInfoType.setCostCenterExtendInfo(costCenterExtendInfoType);
                return costCenterInfoType;
            }
            return null;
        }
        // 按照订单走的成本中心list
        SaveCostCenterInputItem orderCostCenterInputItem = saveCostCenterInputVo.getItems().get("fdefault");
        // 按照出行人走的成本中心map
        Map<String, SaveCostCenterInputItem> passengerCostCenterMap = new HashMap<>();
        saveCostCenterInputVo.getItems().forEach((key, value) -> {
            if ("fdefault".equalsIgnoreCase(key)) {
                return;
            }
            passengerCostCenterMap.put(key, value);
        });
        CostCenterInfoType costCenterInfoType = new CostCenterInfoType();
        costCenterInfoType.setCostCenterType(buildCostCenterType(orderCostCenterInputItem, passengerCostCenterMap));
        // 按人走的成本中心
        if (CollectionUtil.isNotEmpty(passengerCostCenterMap)) {
            List<PassengerCostCenterInfoType> passengerCostCenterList = new ArrayList<>();
            wrapperOfSaveCommonData.getOrderCreateRequestType().getHotelBookPassengerInputs().stream()
                .filter(Objects::nonNull).forEach(hotelBookPassengerInput -> {
                    String id = StringUtil.isNotEmpty(hotelBookPassengerInput.getHotelPassengerInput().getUid()) ?
                        hotelBookPassengerInput.getHotelPassengerInput().getUid() :
                        hotelBookPassengerInput.getHotelPassengerInput().getInfoId();
                    SaveCostCenterInputItem saveCostCenterInputItemPassenger = passengerCostCenterMap.get(id);
                    if (null == saveCostCenterInputItemPassenger) {
                        return;
                    }
                    PassengerCostCenterInfoType passengerCostCenter =
                        buildPassengerCostCenterInfoType(hotelBookPassengerInput, wrapperOfSaveCommonData);
                    passengerCostCenter.setCostCenterList(this.getCostCenterList(saveCostCenterInputItemPassenger));
                    passengerCostCenterList.add(passengerCostCenter);
                });
            costCenterInfoType.setPassengerCostCenterList(passengerCostCenterList);
        } else {
            // 组装出行人信息集合: 没有跟人走的成本中心, 需要传入出行人信息集合, 否则结算平台缺失出行人数据
            costCenterInfoType.setPassengerCostCenterList(
                wrapperOfSaveCommonData.getOrderCreateRequestType().getHotelBookPassengerInputs().stream()
                    .filter(Objects::nonNull).map(hotelBookPassengerInput -> {
                        return buildPassengerCostCenterInfoType(hotelBookPassengerInput, wrapperOfSaveCommonData);
                    }).collect(Collectors.toList()));
        }
        // 按订单走的成本中心列表
        costCenterInfoType.setCostCenterList(this.getCostCenterList(orderCostCenterInputItem));
        // 成本中心扩展信息
        costCenterInfoType.setCostCenterExtendInfo(
            this.getCostCenterExtendInfo(wrapperOfSaveCommonData.getApprovalTextInfoResponseType(),
                orderCostCenterInputItem, wrapperOfSaveCommonData.getOrderCreateRequestType().getApprovalInput(),
                wrapperOfSaveCommonData.getOrderCreateRequestType(), wrapperOfSaveCommonData.getAccountInfo()));
        return costCenterInfoType;
    }

    protected PassengerCostCenterInfoType buildPassengerCostCenterInfoType(
        HotelBookPassengerInput hotelBookPassengerInput, WrapperOfSaveCommonData wrapperOfSaveCommonData) {
        PassengerCostCenterInfoType passengerCostCenter = new PassengerCostCenterInfoType();
        String id = StringUtil.isNotEmpty(hotelBookPassengerInput.getHotelPassengerInput().getUid()) ?
            hotelBookPassengerInput.getHotelPassengerInput().getUid() :
            hotelBookPassengerInput.getHotelPassengerInput().getInfoId();
        // 是否为员工
        if (BooleanUtil.parseStr(true)
            .equalsIgnoreCase(hotelBookPassengerInput.getHotelPassengerInput().getEmployee())) {
            passengerCostCenter.setPassengerUid(hotelBookPassengerInput.getHotelPassengerInput().getUid());
        } else {
            passengerCostCenter.setInfoId(hotelBookPassengerInput.getHotelPassengerInput().getInfoId());
        }
        passengerCostCenter.setPassengerName(OrderCreateProcessorOfUtil.getUseName(hotelBookPassengerInput,
            wrapperOfSaveCommonData.getOrderCreateRequestType().getCityInput().getCityId(),
            wrapperOfSaveCommonData.getCheckAvailContextInfo(),
            wrapperOfSaveCommonData.getQconfigOfCertificateInitConfig()));
        return passengerCostCenter;
    }
    // 成本中心自定义字段1
    private static final int COSTCENTER_CUSTOM_1 = 1;
    // 成本中心自定义字段2
    private static final int COSTCENTER_CUSTOM_2 = 2;

    protected String buildJourneyNoNew(ApprovalTextInfoResponseType approvalTextInfoResponseType,
        ApprovalInput approvalInput, OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo) {
        // 紧急预订场景
        String emergencyName =
            buildEmergencyName(approvalTextInfoResponseType, approvalInput, orderCreateRequestType, accountInfo);
        if (StringUtil.isNotBlank(emergencyName)) {
            return emergencyName;
        }
        // 选择了提前审批/出差申请单号
        if (StringUtils.isNotBlank(
            Optional.ofNullable(approvalInput).map(ApprovalInput::getSubApprovalNo).orElse(null))) {
            return approvalInput.getSubApprovalNo();
        }
        return null;
    }

    private String buildJourneyNo(ApprovalTextInfoResponseType approvalTextInfoResponseType,
        SaveCostCenterInputItem orderCostCenterInputItem, ApprovalInput approvalInput,
        OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo) {
        // 紧急预订场景
        String emergencyName =
            buildEmergencyName(approvalTextInfoResponseType, approvalInput, orderCreateRequestType, accountInfo);
        if (StringUtil.isNotBlank(emergencyName)) {
            return emergencyName;
        }
        // 成本中心中传入了关联行程号 提前审批不会有此单号，因为页面不允许输入
        if (StringUtils.isNotBlank(
            Optional.ofNullable(orderCostCenterInputItem).map(SaveCostCenterInputItem::getJourneyNo).orElse(null))) {
            return orderCostCenterInputItem.getJourneyNo();
        }
        // 成本中心未传入关联行程号 选择了提前审批/出差申请单号
        if (StringUtils.isNotBlank(
            Optional.ofNullable(approvalInput).map(ApprovalInput::getSubApprovalNo).orElse(null))) {
            return approvalInput.getSubApprovalNo();
        }
        return null;
    }

    /**
     * 成本中心扩展信息
     *
     * @param orderCostCenterInputItem 订单相关成本中心信息,
     * @param approvalInput            提前审批信息,
     * @return 结果
     */
    private CostCenterExtendInfoType getCostCenterExtendInfo(ApprovalTextInfoResponseType approvalTextInfoResponseType,
        SaveCostCenterInputItem orderCostCenterInputItem,
        ApprovalInput approvalInput, OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo) {
        CostCenterExtendInfoType costCenterExtendInfoType = new CostCenterExtendInfoType();

        costCenterExtendInfoType.setJourneyNo(
            buildJourneyNo(approvalTextInfoResponseType, orderCostCenterInputItem, approvalInput,
                orderCreateRequestType, accountInfo));
        if (null == orderCostCenterInputItem) {
            return costCenterExtendInfoType;
        }

        costCenterExtendInfoType.setProject(orderCostCenterInputItem.getProjectNo());
        costCenterExtendInfoType.setTripPurpose(orderCostCenterInputItem.getTravelPurpose());

        // 成本中心自定义字段
        List<CustomFieldInfoType> customFieldList = new ArrayList<>();
        if (StringUtil.isNotBlank(orderCostCenterInputItem.getD1())) {
            CustomFieldInfoType customFieldInfoTypeTO = new CustomFieldInfoType();
            customFieldInfoTypeTO.setIndex(COSTCENTER_CUSTOM_1);
            customFieldInfoTypeTO.setCustomField(orderCostCenterInputItem.getD1());
            customFieldList.add(customFieldInfoTypeTO);
        }
        if (StringUtil.isNotBlank(orderCostCenterInputItem.getD2())) {
            CustomFieldInfoType customFieldInfoTypeTO = new CustomFieldInfoType();
            customFieldInfoTypeTO.setIndex(COSTCENTER_CUSTOM_2);
            customFieldInfoTypeTO.setCustomField(orderCostCenterInputItem.getD2());
            customFieldList.add(customFieldInfoTypeTO);
        }
        costCenterExtendInfoType.setCustomFieldList(customFieldList);

        return costCenterExtendInfoType;
    }

    private static final int COSTCENTER_LEVEL_1 = 1;
    private static final int COSTCENTER_LEVEL_2 = 2;
    private static final int COSTCENTER_LEVEL_3 = 3;
    private static final int COSTCENTER_LEVEL_4 = 4;
    private static final int COSTCENTER_LEVEL_5 = 5;
    private static final int COSTCENTER_LEVEL_6 = 6;

    /**
     * 获取成本中心信息
     *
     * @param saveCostCenterInputItem 请求信息
     * @return 结果
     */
    private List<CostCenterItemInfoType> getCostCenterList(SaveCostCenterInputItem saveCostCenterInputItem) {
        if (null == saveCostCenterInputItem) {
            return null;
        }

        List<CostCenterItemInfoType> costCenterItemInfoTypeTOList = new ArrayList<>();
        if (StringUtil.isNotBlank(saveCostCenterInputItem.getCost1())) {
            CostCenterItemInfoType itemInfoTypeTO = new CostCenterItemInfoType();
            itemInfoTypeTO.setLevel(COSTCENTER_LEVEL_1);
            itemInfoTypeTO.setCostCenterValue(saveCostCenterInputItem.getCost1());
            costCenterItemInfoTypeTOList.add(itemInfoTypeTO);
        }
        if (StringUtil.isNotBlank(saveCostCenterInputItem.getCost2())) {
            CostCenterItemInfoType itemInfoTypeTO = new CostCenterItemInfoType();
            itemInfoTypeTO.setLevel(COSTCENTER_LEVEL_2);
            itemInfoTypeTO.setCostCenterValue(saveCostCenterInputItem.getCost2());
            costCenterItemInfoTypeTOList.add(itemInfoTypeTO);
        }
        if (StringUtil.isNotBlank(saveCostCenterInputItem.getCost3())) {
            CostCenterItemInfoType itemInfoTypeTO = new CostCenterItemInfoType();
            itemInfoTypeTO.setLevel(COSTCENTER_LEVEL_3);
            itemInfoTypeTO.setCostCenterValue(saveCostCenterInputItem.getCost3());
            costCenterItemInfoTypeTOList.add(itemInfoTypeTO);
        }
        if (StringUtil.isNotBlank(saveCostCenterInputItem.getCost4())) {
            CostCenterItemInfoType itemInfoTypeTO = new CostCenterItemInfoType();
            itemInfoTypeTO.setLevel(COSTCENTER_LEVEL_4);
            itemInfoTypeTO.setCostCenterValue(saveCostCenterInputItem.getCost4());
            costCenterItemInfoTypeTOList.add(itemInfoTypeTO);
        }
        if (StringUtil.isNotBlank(saveCostCenterInputItem.getCost5())) {
            CostCenterItemInfoType itemInfoTypeTO = new CostCenterItemInfoType();
            itemInfoTypeTO.setLevel(COSTCENTER_LEVEL_5);
            itemInfoTypeTO.setCostCenterValue(saveCostCenterInputItem.getCost5());
            costCenterItemInfoTypeTOList.add(itemInfoTypeTO);
        }
        if (StringUtil.isNotBlank(saveCostCenterInputItem.getCost6())) {
            CostCenterItemInfoType itemInfoTypeTO = new CostCenterItemInfoType();
            itemInfoTypeTO.setLevel(COSTCENTER_LEVEL_6);
            itemInfoTypeTO.setCostCenterValue(saveCostCenterInputItem.getCost6());
            costCenterItemInfoTypeTOList.add(itemInfoTypeTO);
        }
        return costCenterItemInfoTypeTOList;
    }

    public static String COST_CENTER_TYPE_ORDER = "O";
    public static String COST_CENTER_TYPE_PSG = "P";
    public static String COST_CENTER_TYPE_MIX = "A";


    /**
     * 成本中心类型: O-按订单, P-按出行人, 混合-A
     *
     * @param orderCostCenterInputItem
     * @param passengerCostCenterMap
     * @return
     */
    private String buildCostCenterType(SaveCostCenterInputItem orderCostCenterInputItem,
        Map<String, SaveCostCenterInputItem> passengerCostCenterMap) {
        if (null != orderCostCenterInputItem && CollectionUtil.isNotEmpty(passengerCostCenterMap)) {
            return COST_CENTER_TYPE_MIX;
        }
        if (CollectionUtil.isNotEmpty(passengerCostCenterMap)) {
            return COST_CENTER_TYPE_PSG;
        }
        return COST_CENTER_TYPE_ORDER;
    }

    private String getProductLine(WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo) {
        if (checkAvailContextInfo.getRoomTypeEnum() == RoomTypeEnum.M && checkAvailContextInfo.isTmcPrice()) {
            return "C2M";
        }
        return Optional.ofNullable(checkAvailContextInfo.getRoomTypeEnum()).orElse(RoomTypeEnum.M).getValue();
    }

    private String getOrderSource(SourceFrom sourceFrom) {
        if (sourceFrom == null) {
            return "App";
        }
        switch (sourceFrom) {
            case Online:
                return "Online";
            case Offline:
                return "Offline";
            default:
                return "App";
        }
    }

    protected String getPolicyNameCnOrEn(GetCorpUserInfoResponseType policyCorpUserInfo,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo, CityInput cityInput) {
        if (null == policyCorpUserInfo) {
            return null;
        }
        String name = policyCorpUserInfo.getName();
        if (OrderCreateProcessorOfUtil.usePsgEname(cityInput.getCityId(), checkAvailContextInfo)) {
            return getPolicyEnName(policyCorpUserInfo);
        }
        if (StringUtils.isEmpty(name)) {
            return getPolicyEnName(policyCorpUserInfo);
        }
        return name;
    }

    protected String getPolicyEnName(GetCorpUserInfoResponseType policyCorpUserInfo) {
        String nameEn = StringUtils.EMPTY;
        if (StringUtil.isBlank(policyCorpUserInfo.getNameENLastName()) || StringUtil.isBlank(
            policyCorpUserInfo.getNameENFirstName())) {
            return nameEn;
        }
        nameEn = policyCorpUserInfo.getNameENLastName().trim() + "/" + policyCorpUserInfo.getNameENFirstName().trim();
        if (StringUtil.isNotBlank(policyCorpUserInfo.getNameENMiddleName())) {
            nameEn += " " + policyCorpUserInfo.getNameENMiddleName();
        }
        return nameEn;
    }

    private List<TravelControlPreApprovalType> getTravelControlPreApprovals(
        OrderCreateRequestType orderCreateRequestType, CheckTravelPolicyResponseType checkTravelPolicyResponseType,
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
        GetCorpUserInfoResponseType getCorpUserInfoResponseTypePolicy,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
        List<TravelControlPreApprovalType> result = new ArrayList<>();
        if (orderCreateRequestType == null || checkTravelPolicyResponseType == null
            || queryCheckAvailContextResponseType == null) {
            return result;
        }
        HotelPolicyInput hotelPolicyInput = orderCreateRequestType.getHotelPolicyInput();
        String policyUid =
            Optional.ofNullable(hotelPolicyInput).map(HotelPolicyInput::getPolicyInput).map(PolicyInput::getPolicyUid)
                .orElse(null);
        String subApprovalNo = Optional.ofNullable(hotelPolicyInput).map(HotelPolicyInput::getApprovalInput)
            .map(ApprovalInput::getSubApprovalNo).orElse(null);
        if (StringUtil.isNotBlank(policyUid) && StringUtil.isNotBlank(subApprovalNo)) {
            TravelControlPreApprovalType travelControlPreApprovalType = new TravelControlPreApprovalType();
            // 政策执行人 uid
            travelControlPreApprovalType.setUid(policyUid);
            // 政策执行人名称
            if (getCorpUserInfoResponseTypePolicy != null) {
                travelControlPreApprovalType.setPassengerName(
                    getPolicyNameCnOrEn(getCorpUserInfoResponseTypePolicy, checkAvailContextInfo,
                        orderCreateRequestType.getCityInput()));
            }
            // 政策执行人审批单号
            travelControlPreApprovalType.setPreApprovalNo(subApprovalNo);
            travelControlPreApprovalType.setPreApprovalType(PRE_APPROVAL_TYPE_POLICY);
            result.add(travelControlPreApprovalType);
        }

        if (CollectionUtils.isEmpty(orderCreateRequestType.getHotelBookPassengerInputs())) {
            return result;
        }

        // key 为uid, value 为管控结果, 用于后续循环里获取每个用户的管控结果
        Map<String, String> verifyResultMap = new HashMap<>();
        Optional.ofNullable(checkTravelPolicyResponseType.getVerifyPassengerResult())
            .map(VerifyPassengerResultType::getVerifyPassengerDetailList).orElse(new ArrayList<>())
            .forEach(verifyPassengerDetailType -> {
                if (verifyPassengerDetailType == null) {
                    return;
                }
                String id =
                    StringUtil.isNotBlank(verifyPassengerDetailType.getUid()) ? verifyPassengerDetailType.getUid() :
                        verifyPassengerDetailType.getInfoId();
                String verifyResult = verifyPassengerDetailType.getVerifyResult();
                verifyResultMap.put(id, verifyResult);
            });
        for (HotelBookPassengerInput passengerInput : orderCreateRequestType.getHotelBookPassengerInputs()) {
            if (null == passengerInput || !needTravelControlClient(passengerInput, checkTravelPolicyResponseType)) {
                continue;
            }

            TravelControlPreApprovalType travelControlPreApprovalType = new TravelControlPreApprovalType();
            HotelPassengerInput hotelPassengerInput = passengerInput.getHotelPassengerInput();
            if (hotelPassengerInput != null) {
                String id = StringUtil.isNotBlank(hotelPassengerInput.getUid()) ? hotelPassengerInput.getUid() :
                    hotelPassengerInput.getInfoId();
                travelControlPreApprovalType.setPreApprovalNo(
                    Optional.ofNullable(hotelPassengerInput.getApprovalInput()).map(ApprovalInput::getSubApprovalNo)
                        .orElse(null));
                travelControlPreApprovalType.setUid(id);
                String verifyResult = verifyResultMap.get(id);
                if (StringUtil.isNotBlank(verifyResult)) {
                    travelControlPreApprovalType.setPreApprovalResult(
                        String.valueOf(getPrepareAuthStatus(verifyResult)));
                }
                travelControlPreApprovalType.setPassengerNo(TemplateNumberUtil.getValue(TemplateNumberUtil.parseLong(
                    Optional.ofNullable(hotelPassengerInput.getApprovalPassengerId()).orElse("0"))));
            }
            travelControlPreApprovalType.setPreApprovalType("Client");
            travelControlPreApprovalType.setPassengerName(OrderCreateProcessorOfUtil.getUseName(passengerInput,
                OrderCreateProcessorOfUtil.getCityId(orderCreateRequestType), checkAvailContextInfo,
                qconfigOfCertificateInitConfig));
            travelControlPreApprovalType.setExternalEmployeeId(
                Optional.ofNullable(hotelPassengerInput).map(HotelPassengerInput::getExternalEmployeeId).orElse(null));
            result.add(travelControlPreApprovalType);
        }
        return result;
    }

    private boolean needTravelControlClient(HotelBookPassengerInput passengerInput,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        if (StringUtil.equalsIgnoreCase(Optional.ofNullable(checkTravelPolicyResponseType)
            .map(CheckTravelPolicyResponseType::getApprovalBillControlDimension).orElse(null), "P")) {
            return true;
        }

        ApprovalInput approvalInput =
            Optional.ofNullable(passengerInput).map(HotelBookPassengerInput::getHotelPassengerInput)
                .map(HotelPassengerInput::getApprovalInput).orElse(null);
        String subApprovalNo = Optional.ofNullable(approvalInput).map(ApprovalInput::getSubApprovalNo).orElse(null);
        long approvalPassengerId =
            Optional.ofNullable(passengerInput).map(HotelBookPassengerInput::getHotelPassengerInput)
                .map(HotelPassengerInput::getApprovalPassengerId).map(NumberUtil::parseLong).orElse(0L);

        return StringUtils.isNotBlank(subApprovalNo) || approvalPassengerId > 0;
    }

    /**
     * PASS（管控通过） FAIL（管控不通过） UNNECESSARY（无需管控） EMERGENCY（紧急预订）
     *
     * @param verifyResult
     * @return
     */
    private Integer getPrepareAuthStatus(String verifyResult) {
        if (StringUtil.isBlank(verifyResult)) {
            return COMMIT_PRE_APPROVAL_NO_NOTCHECK;
        }
        switch (verifyResult) {
            case EMERGENCY:
                return COMMIT_PRE_APPROVAL_NO_URGENT;
            case PASS:
                return COMMIT_PRE_APPROVAL_NO_PASS;
            case FAIL:
                return COMMIT_PRE_APPROVAL_NO_FAIL;
            default:
                return COMMIT_PRE_APPROVAL_NO_NOTCHECK;
        }
    }

    private MiceInfoType getMiceInfoType(MiceInput miceInput) {
        if (null == miceInput || null == miceInput.getMiceActivityId()) {
            return null;
        }

        MiceInfoType miceInfoType = new MiceInfoType();
        try {
            int miceId = Integer.parseInt(miceInput.getMiceActivityId());
            miceInfoType.setMiceId(miceId);
        } catch (NumberFormatException e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, MapperOfSaveCommonDataRequestType.class,
                "Failed to parse miceId!",
                "MiceActivityId is not a number, miceActivityId: " + miceInput.getMiceActivityId(), null);
            return null;
        }
        miceInfoType.setMiceToken(miceInput.getMiceToken());

        return miceInfoType;
    }

    private List<CorpDockingInfoType> getCorpDockingInfoList(SSOInfoQueryResponseType ssoInfo) {
        List<CorpDockingInfoType> corpDockingInfoList = new ArrayList<>();
        // 钉钉行程单id
        String dingJourneyBizNo =
            Optional.ofNullable(ssoInfo).map(SSOInfoQueryResponseType::getSsoBaseInfo).map(SSOBaseInfo::getSsoDingInfo)
                .map(SSODingInfo::getDingJourneyBizNo).orElse(null);
        if (StringUtil.isNotBlank(dingJourneyBizNo)) {
            CorpDockingInfoType corpDockingInfoType = new CorpDockingInfoType();
            corpDockingInfoType.setDockingOrderId(dingJourneyBizNo);
            corpDockingInfoType.setDockingSource(DOCKING_SOURCE_DINGTALK);
            corpDockingInfoList.add(corpDockingInfoType);
        }

        return corpDockingInfoList.isEmpty() ? null : corpDockingInfoList;
    }

    private List<AttachmentInfoType> getAttachmentInfoList(List<AttachmentInfo> attachmentInfos) {
        if (CollectionUtils.isEmpty(attachmentInfos)) {
            return null;
        }
        List<AttachmentInfoType> result = new ArrayList<>();
        for (AttachmentInfo attachmentInfo : attachmentInfos) {
            if (null == attachmentInfo) {
                continue;
            }
            AttachmentInfoType attachmentInfoType = new AttachmentInfoType();
            attachmentInfoType.setAttachmentName(attachmentInfo.getAttachmentName());
            attachmentInfoType.setAttachmentType(ATTACHMENT_TYPE_APPROVAL);
            attachmentInfoType.setAttachmentUrl(attachmentInfo.getAttachmentUrl());
            result.add(attachmentInfoType);
        }
        return result;
    }

    private ExtendInfoType getExtendInfoType(ApprovalFlowComputeResponseType approvalFlowComputeResponseType,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType,
        MatchApprovalFlowResponseType matchApprovalFlowResponseType, OrderCreateToken orderCreateToken,
        OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo,
        SearchTripDetailResponseType searchTripDetailResponseTypeFolow, CreateTripResponseType createTripResponseType) {
        ExtendInfoType extendInfoType = new ExtendInfoType();
        extendInfoType.setScene("B");
        String ssoKey = Optional.ofNullable(orderCreateRequestType.getSsoInput()).map(SSOInput::getSsoKey).orElse(null);
        extendInfoType.setSsoKey(ssoKey);
        extendInfoType.setApprovalInfo(OrderCreateProcessorOfUtil.buildApprovalInfoType(approvalFlowComputeResponseType,
            matchApprovalFlowResponseType, orderCreateToken, orderCreateRequestType, accountInfo));
        extendInfoType.setPrepareApprovalInfo(
            getPrepareApprovalInfo(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo));
        extendInfoType.setTripInfo(
            getTripInfo(searchTripDetailResponseTypeFolow, orderCreateRequestType, createTripResponseType, accountInfo,
                orderCreateToken));
        return extendInfoType;
    }

    private boolean needPrepareApprovalInfo(OrderCreateRequestType orderCreateRequestType,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType, WrapperOfAccount.AccountInfo accountInfo) {
        if (accountInfo.isTravelApplyRequired(CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
            orderCreateRequestType.getCorpPayInfo())) {
            return true;
        }
        if (accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
            orderCreateRequestType.getCorpPayInfo())) {
            return true;
        }
        if (checkTravelPolicyResponseType != null) {
            if (checkTravelPolicyResponseType.getVerifyApprovalBillResult() != null) {
                return true;
            }
            if (checkTravelPolicyResponseType.getVerifyDockingResult() != null) {
                return true;
            }
        }
        if (useAggControlResult(checkTravelPolicyResponseType)) {
            return true;
        }
        return false;
    }

    private PrepareApprovalInfoType getPrepareApprovalInfo(OrderCreateRequestType orderCreateRequestType,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType,
        WrapperOfAccount.AccountInfo accountInfo) {
        if (!needPrepareApprovalInfo(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo)) {
            return null;
        }
        if (orderCreateRequestType == null || checkTravelPolicyResponseType == null) {
            return null;
        }
        // 未来方向 使用agg计算好的管控结果 按人管控、含下单管控 下单管控+按单管控/纯下单管控
        if (useAggControlResult(checkTravelPolicyResponseType)) {
            return buildPrepareApprovalInfo(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo);
        }
        // 纯按单管控 需要BFF根据入参和agg响应一起计算管控结果
        return buildPrepareApprovalInfoBff(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo);
    }

    private PrepareApprovalInfoType buildPrepareApprovalInfoTypeUseAgg(OrderCreateRequestType orderCreateRequestType,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType, WrapperOfAccount.AccountInfo accountInfo) {
        PrepareApprovalInfoType prepareApprovalInfo = new PrepareApprovalInfoType();
        prepareApprovalInfo.setApprovalVerifyId(
            Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
                .map(VerifyApprovalBillResultType::getTransactionID).orElse(StringUtil.EMPTY));
        prepareApprovalInfo.setPrepareApprovalNo(buildApprovalNoByOrder(accountInfo, orderCreateRequestType));
        if (EMERGENCY.equalsIgnoreCase(Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
            .map(VerifyApprovalBillResultType::getVerifyResult).orElse(null))) {
            prepareApprovalInfo.setPrepareAuthStatus(COMMIT_PRE_APPROVAL_NO_URGENT);
            return prepareApprovalInfo;
        }
        if (PASS.equalsIgnoreCase(Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
            .map(VerifyApprovalBillResultType::getVerifyResult).orElse(null))) {
            prepareApprovalInfo.setPrepareAuthStatus(COMMIT_PRE_APPROVAL_NO_PASS);
            return prepareApprovalInfo;
        }
        if (FAIL.equalsIgnoreCase(Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
            .map(VerifyApprovalBillResultType::getVerifyResult).orElse(null))) {
            prepareApprovalInfo.setPrepareAuthStatus(COMMIT_PRE_APPROVAL_NO_FAIL);
            return prepareApprovalInfo;
        }
        prepareApprovalInfo.setPrepareAuthStatus(COMMIT_PRE_APPROVAL_NO_NOTCHECK);
        return prepareApprovalInfo;
    }

    private PrepareApprovalInfoType buildPrepareApprovalInfoBff(OrderCreateRequestType orderCreateRequestType,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType, WrapperOfAccount.AccountInfo accountInfo) {
        if (orderCreateRequestType.getApprovalInput() == null) {
            return null;
        }
        if (!BooleanUtil.parseStr(Boolean.TRUE).equals(orderCreateRequestType.getApprovalInput().getEmergency())
            && StringUtil.isBlank(orderCreateRequestType.getApprovalInput().getSubApprovalNo())) {
            return null;
        }
        // 使用agg计算好的管控结果
        if (StringUtil.equalsIgnoreCase(checkTravelPolicyResponseType.getApprovalPriceOptimized(), "T")) {
            return buildPrepareApprovalInfoTypeUseAgg(orderCreateRequestType, checkTravelPolicyResponseType,
                accountInfo);
        }
        boolean saveEmergency =
            buildSaveEmergency(orderCreateRequestType.getApprovalInput(), accountInfo, orderCreateRequestType);
        if (!saveEmergency && StringUtil.isBlank(orderCreateRequestType.getApprovalInput().getSubApprovalNo())) {
            return null;
        }
        PrepareApprovalInfoType prepareApprovalInfo = new PrepareApprovalInfoType();
        prepareApprovalInfo.setPrepareApprovalNo(buildApprovalNoByOrder(accountInfo, orderCreateRequestType));
        prepareApprovalInfo.setApprovalVerifyId(
            buildApprovalVerifyId(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo));
        if (BooleanUtil.parseStr(Boolean.TRUE).equals(orderCreateRequestType.getApprovalInput().getEmergency())
            || saveEmergency) {
            prepareApprovalInfo.setPrepareAuthStatus(COMMIT_PRE_APPROVAL_NO_URGENT);
            return prepareApprovalInfo;
        }
        if (checkTravelPolicyResponseType.getVerifyApprovalBillResult() != null) {
            prepareApprovalInfo.setApprovalVerifyId(
                checkTravelPolicyResponseType.getVerifyApprovalBillResult().getTransactionID());
            if (buildApprovalOnPayType(orderCreateRequestType, accountInfo)) {
                if (BooleanUtil.isTrue(checkTravelPolicyResponseType.getVerifyApprovalBillResult().isInControl())) {
                    prepareApprovalInfo.setPrepareAuthStatus(COMMIT_PRE_APPROVAL_NO_PASS);
                } else {
                    prepareApprovalInfo.setPrepareAuthStatus(COMMIT_PRE_APPROVAL_NO_FAIL);
                }
                return prepareApprovalInfo;
            }
        }
        prepareApprovalInfo.setPrepareAuthStatus(COMMIT_PRE_APPROVAL_NO_NOTCHECK);
        return prepareApprovalInfo;
    }

    private String buildApprovalVerifyId(OrderCreateRequestType orderCreateRequestType,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType, WrapperOfAccount.AccountInfo accountInfo) {
        if (accountInfo.isTravelApplyRequired(CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
            orderCreateRequestType.getCorpPayInfo())) {
            if (buildSaveEmergency(orderCreateRequestType.getApprovalInput(), accountInfo, orderCreateRequestType)) {
                return Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
                    .map(VerifyApprovalBillResultType::getTransactionID).orElse(null);
            }
            return Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
                .map(VerifyApprovalBillResultType::getTransactionID).orElse(StringUtil.EMPTY);
        }
        return Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
            .map(VerifyApprovalBillResultType::getTransactionID).orElse(StringUtil.EMPTY);
    }

    private String buildApprovalNoByOrder(WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType) {
        if (!accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
            orderCreateRequestType.getCorpPayInfo()) && !accountInfo.isTravelApplyRequired(
            CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
            orderCreateRequestType.getCorpPayInfo())) {
            return null;
        }
        return Optional.ofNullable(orderCreateRequestType.getApprovalInput()).map(ApprovalInput::getSubApprovalNo)
            .orElse(null);
    }

    private String buildApprovalNo(ApprovalInput approvalInput, WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType) {
        if (accountInfo.isTravelApplyRequired(CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
            orderCreateRequestType.getCorpPayInfo())) {
            return Optional.ofNullable(approvalInput).map(ApprovalInput::getSubApprovalNo).orElse(StringUtil.EMPTY);
        }
        return orderCreateRequestType.getApprovalInput().getSubApprovalNo();
    }
    private boolean buildSaveEmergency(ApprovalInput approvalInput, WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType) {
        if (StringUtil.isNotBlank(approvalInput.getSubApprovalNo())) {
            return false;
        }
        if (!BooleanUtil.parseStr(true).equalsIgnoreCase(approvalInput.getEmergency())) {
            return false;
        }
        return buildApprovalOnPayType(orderCreateRequestType, accountInfo);
    }

    private boolean buildApprovalOnPayType(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        boolean needCheckPayType =
            Arrays.asList(HotelPayTypeEnum.UNION_PAY, HotelPayTypeEnum.SELF_PAY, HotelPayTypeEnum.CASH,
                HotelPayTypeEnum.GUARANTEE_CORP_PAY, HotelPayTypeEnum.GUARANTEE_SELF_PAY).contains(roomPayType);
        if (accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
            orderCreateRequestType.getCorpPayInfo()) || accountInfo.isTravelApplyRequired(
            CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()),
            orderCreateRequestType.getCorpPayInfo())) {
            // 个人需要判断个人支付方式是否开启
            if (needCheckPayType) {
                return accountInfo.isPersonPayNeedAdvanceAuth(
                    CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()));
            }
            // 其他的公帐相关无需判断支付方式，后台支持配置 公帐/公帐+个人
            return true;
        }
        return false;
    }

    private PrepareApprovalInfoType buildPrepareApprovalInfo(OrderCreateRequestType orderCreateRequestType,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType, WrapperOfAccount.AccountInfo accountInfo) {
        PrepareApprovalInfoType prepareApprovalInfoType = new PrepareApprovalInfoType();
        prepareApprovalInfoType.setPrepareApprovalNo(buildApprovalNoByOrder(accountInfo, orderCreateRequestType));
        prepareApprovalInfoType.setPrepareApprovalControl(
            checkTravelPolicyResponseType.getApprovalBillControlDimension());
        if (buildControlByPsg(checkTravelPolicyResponseType)) {
            prepareApprovalInfoType.setApprovalVerifyId(
                checkTravelPolicyResponseType.getVerifyPassengerResult().getTransactionID());
            prepareApprovalInfoType.setPrepareAuthStatus(
                getPrepareAuthStatus(checkTravelPolicyResponseType.getVerifyPassengerResult().getVerifyResult()));
            return prepareApprovalInfoType;
        }
        if (buildControlHasDocking(checkTravelPolicyResponseType)) {
            prepareApprovalInfoType.setApprovalVerifyId(
                Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
                    .map(VerifyApprovalBillResultType::getTransactionID).orElse(null));
            prepareApprovalInfoType.setPrepareAuthStatus(getPrepareAuthStatus(
                Optional.ofNullable(checkTravelPolicyResponseType.getCheckResultExt())
                    .map(CheckResultExtType::getBillAndDockingMergedResult)
                    .map(BillAndDockingMergedResultType::getVerifyResult).orElse(null)));
            return prepareApprovalInfoType;
        }
        if (checkTravelPolicyResponseType.getVerifyApprovalBillResult() != null) {
            prepareApprovalInfoType.setApprovalVerifyId(
                checkTravelPolicyResponseType.getVerifyApprovalBillResult().getTransactionID());
            prepareApprovalInfoType.setPrepareAuthStatus(
                getPrepareAuthStatus(checkTravelPolicyResponseType.getVerifyApprovalBillResult().getVerifyResult()));
        }
        return prepareApprovalInfoType;
    }

    private boolean buildControlByPsg(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        if (StringUtil.equalsIgnoreCase(Optional.ofNullable(checkTravelPolicyResponseType)
            .map(CheckTravelPolicyResponseType::getApprovalBillControlDimension).orElse(null), "P")) {
            return true;
        }
        return false;
    }

    private boolean buildControlOrder(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        if (StringUtil.equalsIgnoreCase(Optional.ofNullable(checkTravelPolicyResponseType)
            .map(CheckTravelPolicyResponseType::getApprovalBillControlDimension).orElse(null), "O")) {
            return true;
        }
        return false;
    }

    private boolean buildControlHasDocking(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        if (checkTravelPolicyResponseType.getVerifyDockingResult() != null) {
            return true;
        }
        return false;
    }

    /**
     * 是否使用agg的管控结果
     * 按人管控
     * 含下单管控  下单管控+按单管控/纯下单管控
     *
     * @param checkTravelPolicyResponseType
     * @return
     */
    private boolean useAggControlResult(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        if (checkTravelPolicyResponseType == null) {
            return false;
        }
        if (buildControlByPsg(checkTravelPolicyResponseType)) {
            return true;
        }
        if (buildControlHasDocking(checkTravelPolicyResponseType)) {
            return true;
        }
        return false;
    }

    private String buildTransactionId(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        if ("P".equalsIgnoreCase(checkTravelPolicyResponseType.getApprovalBillControlDimension())) {
            return Optional.ofNullable(checkTravelPolicyResponseType.getVerifyPassengerResult())
                .map(VerifyPassengerResultType::getTransactionID).orElse(null);
        }
        return Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
            .map(VerifyApprovalBillResultType::getTransactionID).orElse(null);
    }

    private boolean isNeedCheck(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        if (checkTravelPolicyResponseType == null) {
            return false;
        }
        String approvalBillControlDimension = checkTravelPolicyResponseType.getApprovalBillControlDimension();
        if ("P".equals(approvalBillControlDimension)) {
            return checkTravelPolicyResponseType.getVerifyPassengerResult() != null;
        }
        if ("O".equals(approvalBillControlDimension)) {
            return checkTravelPolicyResponseType.getVerifyApprovalBillResult() != null;
        }
        return false;
    }

    private boolean isMatchCheckTravelPolicy(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        if (checkTravelPolicyResponseType == null) {
            return false;
        }
        String approvalBillControlDimension = checkTravelPolicyResponseType.getApprovalBillControlDimension();
        if ("P".equals(approvalBillControlDimension)) {
            String verifyResult = Optional.ofNullable(checkTravelPolicyResponseType.getVerifyPassengerResult())
                .map(VerifyPassengerResultType::getVerifyResult).orElse(null);
            return "PASS".equals(verifyResult);
        }
        if ("O".equals(approvalBillControlDimension)) {
            String verifyResult = Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
                .map(VerifyApprovalBillResultType::getVerifyResult).orElse(null);
            return "PASS".equals(verifyResult);
        }
        return false;
    }

    private TripInfoType getTripInfo(SearchTripDetailResponseType searchTripDetailResponseTypeFolow,
        OrderCreateRequestType orderCreateRequestType, CreateTripResponseType createTripResponseType,
        WrapperOfAccount.AccountInfo accountInfo, OrderCreateToken orderCreateToken) {
        Long tripId = getTripId(accountInfo, orderCreateRequestType, createTripResponseType, orderCreateToken);
        if (ObjectUtil.isNull(tripId) || tripId <= 0) {
            return null;
        }
        TripInfoType result = new TripInfoType();
        result.setTripId(tripId);
        if (chooseUnfolow(orderCreateToken, orderCreateRequestType, accountInfo)) {
            if (buildTripAdditionalOrder(searchTripDetailResponseTypeFolow)) {
                result.setTripAdditionalOrder(true);
            }
            return result;
        }
        long followTripId = OrderCreateProcessorOfUtil.getfollowTripId(orderCreateRequestType, orderCreateToken);
        if (followTripId <= 0) {
            return result;
        }
        result.setAuthFromTripId(true);
        result.setTripAdditionalOrder(true);
        result.setContinueApprovalOrderId(
            OrderCreateProcessorOfUtil.buildFollowOrderNo(orderCreateRequestType, orderCreateToken));
        return result;
    }

    protected boolean buildTripAdditionalOrder(SearchTripDetailResponseType searchTripDetailResponseTypeFolow) {
        if (searchTripDetailResponseTypeFolow == null || CollectionUtil.isEmpty(
            searchTripDetailResponseTypeFolow.getApprovalInfoList())) {
            return false;
        }
        ApprovalInfo approvalInfo =
            Optional.ofNullable(searchTripDetailResponseTypeFolow.getApprovalInfoList()).orElse(new ArrayList<>())
                .stream().filter(Objects::nonNull).findFirst().orElse(null);
        if (approvalInfo == null || StringUtil.isBlank(approvalInfo.getExternalId())) {
            return false;
        }
        if (APPROVAL_STATUS_A.equals(approvalInfo.getStatus()) || APPROVAL_STATUS_T.equals(approvalInfo.getStatus())) {
            return false;
        }
        return true;
    }

    /**
     * 人工/智能 不延用行程时 无需审批和审批通过外的状态走补单流程
     *
     * @param
     * @return
     */
    protected boolean chooseUnfolow(OrderCreateToken orderCreateToken, OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo) {
        if (!orderCreateToken.containsContinueType(ContinueTypeConst.FOLLOW_APPROVAL)) {
            return false;
        }
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() != SourceFrom.Offline) {
            return false;
        }
        if (!accountInfo.isPackageEnabled()) {
            return false;
        }
        if (!CommonConstant.OPEN.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getFollowApprovalInfoInput())
                .map(FollowApprovalInfoInput::getAiFollow).orElse(null)) && !CommonConstant.OPEN.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getFollowApprovalInfoInput())
                .map(FollowApprovalInfoInput::getArtificialFollow).orElse(null))) {

        }
        return !CommonConstant.OPEN.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getFollowApprovalInfoInput())
                .map(FollowApprovalInfoInput::getFollowSelected).orElse(null));
    }

    /**
     * 无需审批
     */
    private static final String APPROVAL_STATUS_A = "A";
    /**
     * 审批通过
     */
    private static final String APPROVAL_STATUS_T = "T";

    private TripInfoType getTripInfoTypeOffline(SearchTripDetailResponseType searchTripDetailResponseType,
        OrderCreateRequestType orderCreateRequestType, TripInfoType result) {
        if (searchTripDetailResponseType == null || orderCreateRequestType == null) {
            return result;
        }
        FollowApprovalInfoInput followApprovalInfoInput = orderCreateRequestType.getFollowApprovalInfoInput();
        // 用户选择了沿用, 并且是在可沿用场景下
        String followSelected =
            Optional.ofNullable(followApprovalInfoInput).map(FollowApprovalInfoInput::getFollowSelected).orElse(null);
        OrderCreateToken orderCreateToken =
            TokenParseUtil.parseOrderCreateToken(orderCreateRequestType.getOrderCreateToken(),
                "T".equalsIgnoreCase(orderCreateRequestType.getUseNewOrderCreate()));
        String canFollowApproval = Optional.ofNullable(orderCreateToken.getFollowApprovalResult())
            .map(FollowApprovalResult::getCanFollowApproval).orElse(null);
        if (BooleanUtil.parseStr(Boolean.TRUE).equals(followSelected) && BooleanUtil.parseStr(Boolean.TRUE)
            .equals(canFollowApproval)) {
            long authFromOrderId = Long.parseLong(
                Optional.ofNullable(followApprovalInfoInput).map(FollowApprovalInfoInput::getFollowOrderId)
                    .orElse("0"));
            result.setAuthFromTripId(true);
            result.setTripAdditionalOrder(true);
            result.setContinueApprovalOrderId(authFromOrderId);
        }
        ApprovalInfo approvalInfo =
            Optional.ofNullable(searchTripDetailResponseType.getApprovalInfoList()).orElse(new ArrayList<>()).stream()
                .filter(Objects::nonNull).findFirst().orElse(null);
        if (approvalInfo == null) {
            return result;
        }
        if (StringUtil.isBlank(approvalInfo.getExternalId())) {
            return result;
        }

        if (APPROVAL_STATUS_A.equals(approvalInfo.getStatus()) || APPROVAL_STATUS_T.equals(approvalInfo.getStatus())) {
            return result;
        }
        result.setTripAdditionalOrder(true);
        return result;
    }

    protected ReservationInfoType getReservationInfo(ReservationInfo reservationInfo,
        OrderCreateRequestType orderCreateRequestType) {
        String reservationType =
            Optional.ofNullable(reservationInfo).map(ReservationInfo::getReservationType).orElse(null);
        if (StringUtil.isNotBlank(reservationType)) {
            ReservationInfoType reservationInfoType = new ReservationInfoType();
            reservationInfoType.setType(reservationType);
            return reservationInfoType;
        }
        DistinguishReservationEnum distinguishReservationEnum =
            OrderCreateProcessorOfUtil.buildDistinguishReservationEnum(orderCreateRequestType.getCorpPayInfo(),
                orderCreateRequestType.getIntegrationSoaRequestType(),
                orderCreateRequestType.getHotelBookPassengerInputs());
        if (distinguishReservationEnum == DistinguishReservationEnum.EMPLOYEE_TRAVEL) {
            ReservationInfoType reservationInfoType = new ReservationInfoType();
            reservationInfoType.setType(DistinguishReservationEnum.EMPLOYEE_TRAVEL.getCode());
            return reservationInfoType;
        }
        return null;
    }

    /**
     * 获取行程号, 从三个地方依次获取
     *
     * @param createTripResponseType
     * @return
     */
    private Long getTripId(WrapperOfAccount.AccountInfo accountInfo, OrderCreateRequestType orderCreateRequestType,
        CreateTripResponseType createTripResponseType, OrderCreateToken orderCreateToken) {
        return OrderCreateProcessorOfUtil.getTripId(accountInfo, orderCreateRequestType, createTripResponseType,
            orderCreateToken);
    }
}
