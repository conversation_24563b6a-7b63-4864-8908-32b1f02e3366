package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyApprovalBillResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyDockingResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyPassengerDetailType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyPassengerResultType
import com.ctrip.corp.approve.ws.contract.FlowTmpl
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowResponseType
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderCheckResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ApprovalInfoResult
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowApprovalResult
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.SaveCostCenterInputItem
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.common.serialize.ProtobufSerializerUtil
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.Approver
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AttachmentInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.MiceInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput
import com.ctrip.corp.bff.hotel.book.common.enums.DistinguishReservationEnum
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfSaveCommonData
import com.ctrip.corp.bff.hotel.book.contract.CostCenterInfo
import com.ctrip.corp.bff.hotel.book.contract.FollowApprovalInfoInput
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.ReservationInfo
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCodeMappingConfig
import com.ctrip.corp.bff.profile.contract.SSOBaseInfo
import com.ctrip.corp.bff.profile.contract.SSODingInfo
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType
import com.ctrip.corp.bff.specific.contract.ApprovalFlowComputeResponseType
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType
import com.ctrip.corp.bff.tools.contract.DataInfo
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.soa._20183.ApprovalInfoType
import com.ctrip.soa._20183.AttachmentInfoType
import com.ctrip.soa._20183.CorpDockingInfoType
import com.ctrip.soa._20183.CostCenterInfoType
import com.ctrip.soa._20183.PrepareApprovalInfoType
import com.ctrip.soa._20183.ReservationInfoType
import com.ctrip.soa._20183.TripInfoType
import com.ctrip.soa._21234.ApprovalInfo
import com.ctrip.soa._21234.CreateTripResponseType
import com.ctrip.soa._21234.SearchTripDetailResponseType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

import java.time.LocalDateTime
import java.time.ZoneOffset

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/13
 */
class MapperOfSaveCommonDataRequestTypeTest extends Specification{
    def myTestClass = new MapperOfSaveCommonDataRequestType()

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "test getTravelControlPreApprovals"() {
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelPolicyInput: new HotelPolicyInput(
                        policyInput: new PolicyInput(
                                "policyUid" : "policyUid",
                        ),
                        approvalInput: new ApprovalInput(
                                "subApprovalNo" : "subApprovalNo",
                                "masterApprovalNo" : "masterApprovalNo",
                                "emergency" : "T",
                        ),
                ),
                "hotelBookPassengerInputs": [
                        new HotelBookPassengerInput(
                                hotelPassengerInput: new HotelPassengerInput(
                                "uid" : "uid11",
                                "approvalInput" : new ApprovalInput(
                                        "subApprovalNo" : "subApprovalNo11",
                                        "masterApprovalNo" : "masterApprovalNo11",
                                        "emergency" : "T",
                                ),
                                "approvalPassengerId" : "11"),
                                "name" : "name11"
                        )
                ],
                "cityInput" : new CityInput(
                        "cityId" : 24
                )
        )
        CheckTravelPolicyResponseType checkTravelPolicyResponseType = new CheckTravelPolicyResponseType(
                "verifyPassengerResult": new VerifyPassengerResultType(
                        "verifyPassengerDetailList": [
                                new VerifyPassengerDetailType(
                                       "uid" : "uid11",
                                        "verifyResult" : "EMERGENCY"
                                )
                        ]
                )
        )
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(balanceType: "PP"))
        GetCorpUserInfoResponseType getCorpUserInfoResponseType = new GetCorpUserInfoResponseType(
                "name" : "policyName",
                nameENFirstName: "nameENFirstName",
                nameENMiddleName: "nameENMiddleName",
                nameENLastName: "nameENLastName"
        )
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo();

        when:
        def result = myTestClass.getTravelControlPreApprovals(
                orderCreateRequestType, checkTravelPolicyResponseType,
                queryCheckAvailContextResponseType, getCorpUserInfoResponseType, checkAvailInfo, null)

        then:
        result.size() == 2
        result.get(0).getPreApprovalType() ==  "Policy"
        result.get(0).getPreApprovalNo() == "subApprovalNo"
        result.get(0).getPreApprovalResult() == null
        result.get(0).getUid() == "policyUid"
        result.get(0).getPassengerName() == "nameENLastName/nameENFirstName nameENMiddleName"

        result.get(1).getPreApprovalType() ==  "Client"
        result.get(1).getPreApprovalNo() == "subApprovalNo11"
        result.get(1).getPreApprovalResult() == "3"
        result.get(1).getUid() == "uid11"
        result.get(1).getPassengerNo() == 11
        result.get(1).getPassengerName() == ""
    }

    def "test getMiceInfo"() {
        given:
        MiceInput miceInput = new MiceInput(
                "miceToken" : "miceToken",
                "miceActivityId" : "12345",
        )


        when:
        def result = myTestClass.getMiceInfoType(miceInput)

        then:
        result.getMiceToken() == "miceToken"
        result.getMiceId() == 12345
    }

    def "test getCorpDockingInfoList"() {
        given:
        SSOInfoQueryResponseType ssoInfo = new SSOInfoQueryResponseType(
                "ssoBaseInfo": new SSOBaseInfo(
                        "ssoDingInfo": new SSODingInfo(
                                "dingJourneyBizNo" : "dingJourneyBizNo"
                        )
                )
        );


        when:
        def result = myTestClass.getCorpDockingInfoList(ssoInfo);

        then:
        result.get(0).getDockingOrderId() == "dingJourneyBizNo"
        result.get(0).getDockingSource() == "DingTalk"
    }

    def "test getAttachmentInfoList"() {
        given:
        List<AttachmentInfo> attachmentInfos = [
                new AttachmentInfo(
                        "attachmentName" : "attachmentName1",
                        "attachmentUrl" : "attachmentUrl1",
                ),
                null,
                new AttachmentInfo(
                        "attachmentName" : "attachmentName2",
                        "attachmentUrl" : "attachmentUrl2",
                ),
        ]


        when:
        def result = myTestClass.getAttachmentInfoList(attachmentInfos);

        then:
        result.size() == 2
        result.get(0).getAttachmentName() == "attachmentName1"
        result.get(0).getAttachmentUrl() == "attachmentUrl1"
        result.get(0).getAttachmentType() == "Approval"

        result.get(1).getAttachmentName() == "attachmentName2"
        result.get(1).getAttachmentUrl() == "attachmentUrl2"
        result.get(1).getAttachmentType() == "Approval"
    }

    def "test getReservationInfo"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if (corpId == "testclose") {
                    return false
                }
                return true
            }
        }
        CorpPayInfo corpPayInfo = new CorpPayInfo(corpPayType: "public")
        UserInfo userInfo = new UserInfo(corpId: "testopen", userId: "u1")
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: userInfo)
        List<HotelBookPassengerInput> hotelBookPassengerInputs = ["u1"].collect {
            new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: it))
        }

        when:
        def result = myTestClass.getReservationInfo(new ReservationInfo(reservationType: "reservationType"), null);

        then:
        result.getType() == "reservationType"

        when:
        result = myTestClass.getReservationInfo(new ReservationInfo(reservationType: ""), new OrderCreateRequestType(
                integrationSoaRequestType: integrationSoaRequestType,
                hotelBookPassengerInputs: hotelBookPassengerInputs,
                corpPayInfo: corpPayInfo
        ));

        then:
        result.getType() == "EmployeeTravel"
    }


    def "test getPrepareApprovalInfo"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                "approvalInput": new ApprovalInput(
                        "subApprovalNo" : "subApprovalNo",
                        "emergency" : "T",
                ),
                "integrationSoaRequestType": new IntegrationSoaRequestType(
                        "transactionID" : "transactionID"
                ),
                "cityInput" : new CityInput(
                        "cityId" : 24
                ),
                "corpPayInfo" : new CorpPayInfo(
                        "corpPayType" : "public"
                )
        )

        CheckTravelPolicyResponseType checkTravelPolicyResponseType = new CheckTravelPolicyResponseType(
                approvalBillControlDimension: "O",
                verifyApprovalBillResult: new VerifyApprovalBillResultType("transactionID" : "transactionID"))

        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "A")
                    put("isChkaheadapproveHotelI","T")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        when:
        def result = myTestClass.getPrepareApprovalInfo(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo);

        then:
        result.getApprovalVerifyId() == "transactionID"
        result.getPrepareApprovalNo() == "subApprovalNo"
        result.getPrepareAuthStatus() == 3


        when:
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "B")
                    put("isChkaheadapproveHotelI","T")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        result = myTestClass.getPrepareApprovalInfo(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo);

        then:
        result.getApprovalVerifyId() == "transactionID"
        result.getPrepareApprovalNo() == "subApprovalNo"
        result.getPrepareAuthStatus() == 3

        when:
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "B")
                    put("isChkaheadapproveHotelI","F")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        result = myTestClass.getPrepareApprovalInfo(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo);

        then:
        result.getApprovalVerifyId() == "transactionID"
        result.getPrepareApprovalNo() == null
        result.getPrepareAuthStatus() == 3


        when:
        checkTravelPolicyResponseType.verifyApprovalBillResult = null
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "B")
                    put("isChkaheadapproveHotelI","F")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        result = myTestClass.getPrepareApprovalInfo(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo);

        then:
        result == null
    }

    @Unroll
    def "test needPrepareApprovalInfo"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                "approvalInput": new ApprovalInput(
                        "subApprovalNo": "subApprovalNo",
                        "emergency": "T",
                ),
                "integrationSoaRequestType": new IntegrationSoaRequestType(
                        "transactionID": "transactionID"
                ),
                "cityInput": new CityInput(
                        "cityId": 24
                ),
                "corpPayInfo": new CorpPayInfo(
                        "corpPayType": "public"
                )
        )

        CheckTravelPolicyResponseType checkTravelPolicyResponseType = new CheckTravelPolicyResponseType(
                verifyApprovalBillResult: verifyApprovalBillResult,
                approvalBillControlDimension: approvalBillControlDimension,
                verifyDockingResult: verifyDockingResult)

        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "A")
                        put("BillControlMode", BillControlMode)
                        put("isChkaheadapproveHotelI", isChkaheadapproveHotelI)
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        expect:
        result == myTestClass.needPrepareApprovalInfo(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo);

        where:
        billControlMode | isChkaheadapproveHotelI | verifyApprovalBillResult           | verifyDockingResult           | approvalBillControlDimension || result
        "A"             | "T"                     | new VerifyApprovalBillResultType() | null                          | "O"                          || true
        "B"             | "T"                     | new VerifyApprovalBillResultType() | null                          | "O"                          || true
        "B"             | "F"                     | null                               | null                          | "O"                          || false
        "B"             | "F"                     | null                               | new VerifyDockingResultType() | "O"                          || true
        "B"             | "F"                     | null                               | null                          | "P"                          || true
    }

    def "test buildApprovalNoByOrder"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                "approvalInput": new ApprovalInput(
                        "subApprovalNo" : "subApprovalNo",
                        "emergency" : "T",
                ),
                "integrationSoaRequestType": new IntegrationSoaRequestType(
                        "transactionID" : "transactionID"
                ),
                "cityInput" : new CityInput(
                        "cityId" : 24
                ),
                "corpPayInfo" : new CorpPayInfo(
                        "corpPayType" : "public"
                )
        )
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "A")
                    put("isChkaheadapproveHotelI","T")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        when:
        def result = myTestClass.buildApprovalNoByOrder(accountInfo, orderCreateRequestType);

        then:
        result == "subApprovalNo"


        when:
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "B")
                    put("isChkaheadapproveHotelI","T")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        result = myTestClass.buildApprovalNoByOrder(accountInfo, orderCreateRequestType);

        then:
        result == "subApprovalNo"

        when:
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {{
                    put("BillType", "A")
                    put("BillControlMode", "B")
                    put("isChkaheadapproveHotelI","F")
                }}))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        result = myTestClass.buildApprovalNoByOrder(accountInfo, orderCreateRequestType);

        then:
        result == null
    }

    def "test getTripInfo"() {
        given:
        SearchTripDetailResponseType searchTripDetailResponseType = new SearchTripDetailResponseType(
                "approvalInfoList": [
                        new ApprovalInfo(
                                "externalId": "externalId",
                        )
                ]
        )
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                "followApprovalInfoInput": new FollowApprovalInfoInput(
                        "followSelected": "T"
                ),
                "tripInput": new TripInput(
                        "tripId": "1122"
                ),
                integrationSoaRequestType: new IntegrationSoaRequestType()
        )
        CreateTripResponseType createTripResponseType = new CreateTripResponseType()
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "E")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        OrderCreateToken orderCreateToken = new OrderCreateToken(followApprovalResult: new FollowApprovalResult())
        orderCreateToken.followApprovalResult.tripId = "7788"
        orderCreateToken.followApprovalResult.followOrderNo = "34343"
        when:
        def result = myTestClass.getTripInfo(
                searchTripDetailResponseType, orderCreateRequestType, createTripResponseType, accountInfo, orderCreateToken);

        then:
        result.getContinueApprovalOrderId() == 34343
        result.getAuthFromTripId() == true
        result.getTripAdditionalOrder() == true


        when:
        orderCreateToken.followApprovalResult.followOrderNo = null
        result = myTestClass.getTripInfo(
                searchTripDetailResponseType, orderCreateRequestType, createTripResponseType, accountInfo, orderCreateToken);

        then:
        result.getContinueApprovalOrderId() == 0
        result.getAuthFromTripId() == true
        result.getTripAdditionalOrder() == true
    }


    @Unroll
    def "testGetProductLine with different scenarios"() {
        given:
        MapperOfSaveCommonDataRequestType mapper = new MapperOfSaveCommonDataRequestType()
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(
                roomInfo: new BookRoomInfoEntity(balanceType: "PP", roomType: roomType, tmcPriceType: tmcPriceType))
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo();

        when: "calling getProductLine with specific parameters"
        def result = mapper.getProductLine(checkAvailInfo)

        then: "the result should be as expected"
        result == expectedResult

        where:
        roomType | tmcPriceType || expectedResult
        "M"      | "M"          || "C2M"
        "M"      | "C"          || "C2M"
        "C"      | "M"          || "C2M"
        "M"      | "C"          || "C2M"
        "C"      | "C"          || "C"
        "C"      | "NONE"       || "C"
        "M"      | "NONE"       || "M"
    }

    def "testBuildApprovalVerifyId with default scenario"() {
        given:
        MapperOfSaveCommonDataRequestType mapper = new MapperOfSaveCommonDataRequestType()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                cityInput: new CityInput(cityId: 1),
                approvalInput: new ApprovalInput(subApprovalNo: "subApprovalNo"),
                corpPayInfo: new CorpPayInfo(corpPayType: "public")
        )
        CheckTravelPolicyResponseType checkTravelPolicyResponseType = new CheckTravelPolicyResponseType(
                verifyApprovalBillResult: new VerifyApprovalBillResultType(transactionID: "transactionID")
        )
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>(){
                    {
                        put("BillControlMode", "B")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();

        when:
        String result = mapper.buildApprovalVerifyId(orderCreateRequestType, checkTravelPolicyResponseType, accountInfo)

        then:
        result == "transactionID"


        when:
        WrapperOfAccount.AccountInfo accountInfoA = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>(){
                    {
                        put("BillControlMode", "A")
                        put("isChkaheadapproveHotelI", "T")
                        put("isChkaheadapproveHotel", "T")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        result = mapper.buildApprovalVerifyId(orderCreateRequestType, checkTravelPolicyResponseType, accountInfoA)

        then:
        result == "transactionID"
    }

    @Unroll
    def "testBuildCostCenterInfoType with different scenarios"() {
        given:
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>(){
                    {
                        put("BillType", "A")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        MapperOfSaveCommonDataRequestType mapper = new MapperOfSaveCommonDataRequestType()
        HotelBookPassengerInput hotelBookPassengerInput = JsonUtil.fromJson("{\n" +
                "        \"hotelPassengerInput\": {\n" +
                "            \"uid\": \"_SL2236978295\",\n" +
                "            \"employee\": \"T\",\n" +
                "            \"external\": \"F\",\n" +
                "            \"employeeId\": \"编XC295295123\"\n" +
                "        },\n" +
                "        \"passengerBasicInfo\": {\n" +
                "            \"preferFirstName\": \"PERIAYYA SARAVANAKUMAR\",\n" +
                "            \"preferLastName\": \"MUTHUKUMMAR\",\n" +
                "            \"gender\": \"U\",\n" +
                "            \"birth\": \"1990-11-08\"\n" +
                "        },\n" +
                "        \"name\": \"彭彭一\",\n" +
                "        \"isRegistrant\": \"F\",\n" +
                "        \"phoneInfo\": {\n" +
                "            \"countryCode\": \"86\",\n" +
                "            \"phoneNo\": \"***********\",\n" +
                "            \"transferPhoneNo\": \"***********\"\n" +
                "        },\n" +
                "        \"nationalityInfo\": {\n" +
                "            \"nationalityCode\": \"CN\"\n" +
                "        }\n" +
                "    }", HotelBookPassengerInput.class)
        WrapperOfSaveCommonData wrapperOfSaveCommonData = WrapperOfSaveCommonData.builder()
                .setAccountInfo(accountInfo)
                .setOrderCreateRequestType(new OrderCreateRequestType(
                        cityInput: new CityInput(cityId: 1),
                        hotelBookPassengerInputs: [hotelBookPassengerInput],
                        corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                        approvalInput: new ApprovalInput(subApprovalNo: "***********"),
                        costCenterInfo: new CostCenterInfo(costCenterJsonString: "{\"items\":{\"fdefault\":{\"cost1\":\"\",\"cost3\":\"测试1\",\"TravelPurpose\":\"出差\",\"ProjectNo\":\"项目测试1\",\"journeyNo\":\"测试关联审批单\",\"D1\":\"\",\"D2\":\"\"},\"_SL2236978295\":{\"name\":\"彭彭一\",\"cost2\":\"测试3\"}}}")))
                .build();

        when:
        CostCenterInfoType result = mapper.buildCostCenterInfoType(wrapperOfSaveCommonData)

        then:
        result.costCenterExtendInfo.journeyNo == "测试关联审批单"
    }

    @Unroll
    def "testBuildJourneyNo with different scenarios"() {
        given:
        ApprovalTextInfoResponseType approvalTextInfoResponseType = new ApprovalTextInfoResponseType(emergencyName: "紧急预订")
        SaveCostCenterInputItem orderCostCenterInputItem = new SaveCostCenterInputItem(journeyNo: journeyNo)
        ApprovalInput approvalInput = new ApprovalInput(subApprovalNo: subApprovalNo, emergency: emergency)
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                cityInput: new CityInput(cityId: 22249),
                corpPayInfo: new CorpPayInfo(corpPayType: "public")
        )
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "A")
                        put("docChooseDimensionHtl", "O")
                        put("isChkaheadapproveHotel", "T")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();

        when:
        String result = new MapperOfSaveCommonDataRequestType().buildJourneyNo(approvalTextInfoResponseType, orderCostCenterInputItem, approvalInput, orderCreateRequestType, accountInfo)

        then:
        result == expectedJourneyNo

        where:
        journeyNo    | subApprovalNo | emergency || expectedJourneyNo
        "journey123" | null          | "F"       || "journey123"
        null         | "approval456" | "F"       || "approval456"
        null         | null          | "F"       || null
        "journey789" | "approval789" | "F"       || "journey789"
        "journey789" | "approval789" | "T"       || "journey789"
        "journey789" | ""            | "T"       || "journey789"
    }

    def "buildJourneyNoNew"() {
        given: "Mocked dependencies for default case"
        def approvalTextInfoResponseType = Mock(ApprovalTextInfoResponseType)
        def approvalInput = Mock(ApprovalInput) {
            getEmergency() >> "F"
            getSubApprovalNo() >> "approvalno"
        }
        def orderCreateRequestType = Mock(OrderCreateRequestType) {
            getCityInput() >> Mock(CityInput) {
                getCityId() >> "123"
            }
            getCorpPayInfo() >> Mock(CorpPayInfo)
        }
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPreApprovalRequired(_, _) >> true
        }
        def mapper = Spy(MapperOfSaveCommonDataRequestType)
        mapper.buildEmergencyName(_,_,_,_) >> "紧急预订" >> null

        when: "test emgrency"
        def result = mapper.buildJourneyNoNew(approvalTextInfoResponseType, approvalInput, orderCreateRequestType, accountInfo)

        then: "The result should be emgrency"
        result == "紧急预订"


        when: "test approvalno"
        result = mapper.buildJourneyNoNew(approvalTextInfoResponseType, approvalInput, orderCreateRequestType, accountInfo)

        then: "The result should be approvalno"
        result == "approvalno"


        when: "test approvalno"
        result = mapper.buildJourneyNoNew(approvalTextInfoResponseType, null, orderCreateRequestType, accountInfo)

        then: "The result should be approvalno"
        result == null
    }
}
