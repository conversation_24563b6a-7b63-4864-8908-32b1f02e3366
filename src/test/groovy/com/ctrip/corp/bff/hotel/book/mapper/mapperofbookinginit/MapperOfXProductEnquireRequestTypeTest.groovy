package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataRequestType
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;

class MapperOfXProductEnquireRequestTypeTest extends Specification {


    def tester = Spy(MapperOfXProductEnquireRequestType)

    def savePoint = new mockit.internal.state.SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert"() {
        expect:
        tester.convert(Tuple2.of(null, null)) == null
        tester.convert(Tuple2.of(null, new QueryHotelOrderDataRequestType(orderId: 2))).orderId == 2

    }
}

