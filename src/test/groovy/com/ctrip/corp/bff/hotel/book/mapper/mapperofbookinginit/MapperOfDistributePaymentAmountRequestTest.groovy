package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.agg.hotel.expense.contract.model.BaseChargeAmount
import com.ctrip.corp.agg.hotel.expense.contract.model.ChargeAmountInfoType
import com.ctrip.corp.bff.framework.hotel.entity.contract.AddPriceInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceDetailInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BffCorpXProductTypeEnum
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.CorpXProductInfoToken
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.hotel.book.processor.ProcessorOfBookingInit
import com.ctrip.corp.hotelbook.commonws.entity.DistributePaymentAmountRequestType
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType
import com.ctrip.model.CalculateServiceChargeV2ResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2025/4/15 15:47
 *
 */
class MapperOfDistributePaymentAmountRequestTest extends Specification {
    def tester = new MapperOfDistributePaymentAmountRequest()


    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "testCheckGetSupportedPaymentMethodResponseType with invalid response"() {
        given: "A ProcessorOfBookingInit instance and a null GetSupportedPaymentMethodResponseType"
        new MockUp<BFFSharkUtil>() {
            @mockit.Mock
            public static String getSharkValue(String key) {
                if (key.contains("666")) {
                    return null
                }
                return key + ":value"
            }
        }

        when: "Calling checkGetSupportedPaymentMethodResponseType"
        tester.checkCalculateServiceChargeV2ResponseType(null)

        then: "An exception is thrown"
        def exception = thrown(BusinessException)
        exception.errorCode == 666
        exception.friendlyMessage == "key.corp.hotel.CorpAggHotelExpenseServiceClient.calculateServiceChargeV2.errorMsg:value"

        when: "Calling checkGetSupportedPaymentMethodResponseType"
        tester.checkCalculateServiceChargeV2ResponseType(new CalculateServiceChargeV2ResponseType(responseCode: null))

        then: "An exception is thrown"
        exception = thrown(BusinessException)
        exception.errorCode == 666
        exception.friendlyMessage == "key.corp.hotel.CorpAggHotelExpenseServiceClient.calculateServiceChargeV2.errorMsg:value"


        when: "Calling checkGetSupportedPaymentMethodResponseType"
        tester.checkCalculateServiceChargeV2ResponseType(new CalculateServiceChargeV2ResponseType(responseCode: 6767))

        then: "An exception is thrown"
        exception = thrown(BusinessException)
        exception.errorCode == 666
        exception.friendlyMessage == "key.corp.hotel.CorpAggHotelExpenseServiceClient.calculateServiceChargeV2.errorMsg.6767:value"

        when: "Calling checkGetSupportedPaymentMethodResponseType"
        tester.checkCalculateServiceChargeV2ResponseType(new CalculateServiceChargeV2ResponseType(responseCode: 20000))

        then: "no exception is thrown"
        noExceptionThrown()
    }

    def "buildAddPriceAmount" () {
        expect:
        new MapperOfDistributePaymentAmountRequest().buildAddPriceAmount(null, null, false, null) == null
        new MapperOfDistributePaymentAmountRequest().buildAddPriceAmount(null, null, true, null) == null
        new MapperOfDistributePaymentAmountRequest().buildAddPriceAmount(null, new AddPriceInput(), true, null) == null
        new MapperOfDistributePaymentAmountRequest().buildAddPriceAmount(null, new AddPriceInput(amountInfo: new AmountInfo()), true, null) == null
        new MapperOfDistributePaymentAmountRequest().buildAddPriceAmount(null, new AddPriceInput(amountInfo: new AmountInfo(amount: "2")), true, new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-05-01", checkOut: "2025-05-02"), roomQuantity: 1)).price == 2
        new MapperOfDistributePaymentAmountRequest().buildAddPriceAmount("CNY", new AddPriceInput(amountInfo: new AmountInfo(amount: "-1")), true,  new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-05-01", checkOut: "2025-05-02"), roomQuantity: 1)) == null
        new MapperOfDistributePaymentAmountRequest().buildAddPriceAmount("CNY", new AddPriceInput(amountInfo: new AmountInfo(amount: "2")), true,  new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-05-01", checkOut: "2025-05-02"), roomQuantity: 1)).price == 2
        new MapperOfDistributePaymentAmountRequest().buildAddPriceAmount("CNY", new AddPriceInput(amountInfo: new AmountInfo(amount: "2")), true,  new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-05-01", checkOut: "2025-05-02"), roomQuantity: -1))?.price == null
    }

    def "buildCorpXProductInfoTypes" () {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            public static <T> T parseToken(String token, Class<T> clazz) {
                return new CorpXProductInfoToken(priceMark: "a", bffCorpXProductTypeEnum: BffCorpXProductTypeEnum.INSURANCE) as T
            }
        }
        expect:
        new MapperOfDistributePaymentAmountRequest().buildCorpXProductInfoTypes(null) == []
        new MapperOfDistributePaymentAmountRequest().buildCorpXProductInfoTypes(new HotelInsuranceInput(hotelInsuranceDetailInputs: [new HotelInsuranceDetailInput(insuranceToken: "a"), new HotelInsuranceDetailInput(), null]))*.priceMark == []
    }

    def "getRequestBaseInfoType" () {
        expect:
        new MapperOfDistributePaymentAmountRequest().getRequestBaseInfoType(new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Offline,userInfo: new UserInfo(userId: "a")))).userInfo.uid == "a"
    }

    def "buildChargeAmount" () {
        expect:
        new MapperOfDistributePaymentAmountRequest().buildChargeAmount(null, null, null, null)
        new MapperOfDistributePaymentAmountRequest().buildChargeAmount(new DistributePaymentAmountRequestType(), new ChargeAmountInfoType(chargeAmountPack: new BaseChargeAmount()), null, null)
    }
}
