package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType
import com.ctrip.soa._20184.OriginalOrderInfoType
import com.ctrip.soa._21234.BasicInfo
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.HotelAreaInfoType
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.RoomInfoType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import mockit.Mock
import mockit.MockUp
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.PaymentInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.MultipleLanguageText
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderBasicInfoType
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderClientInfoType
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QconfigOfInitConfig;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowApprovalResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.shark.currencytemplate.CurrencyDisplayUtil;
import com.ctrip.corp.bff.framework.template.common.shark.entity.CurrencyDisplayInfo;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowReuseInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowReuseInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceNameContent;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ReuseOrderInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ReuseResultDetail;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ReuseResultInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.MathUtils;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfApprovalFlowReuse;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalFlowReuse;
import com.ctrip.corp.bff.hotel.book.contract.HotelReuseOrderInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity;
import com.ctrip.ibu.platform.shark.sdk.api.L10n;
import com.ctrip.soa._20184.CheckHotelAuthExtensionResponseType;
import com.ctrip.soa._20184.QueryHotelAuthExtensionResponseType;
import com.ctrip.soa._21234.SearchTripBasicInfoResponseType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.HotelInfoType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailInfoType;
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
class MapperOfApprovalFlowReuseResponseTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {
        new MockUp<StringUtil>() {
            @Mock
            static boolean isBlank(String s) { return s == null || s.trim() == "" }
        }
        new MockUp<CurrencyDisplayUtil>() {
            @Mock
            static String currencyString(CurrencyDisplayInfo info) { return "MOCKED" }
        }
        new MockUp<StringUtil>() {
            @Mock
            static boolean isBlank(String s) { return s == null || s.trim() == "" }
        }
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return key
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }


    def "convert should return (false, null) when token contains APPROVAL_FLOW_REUSE"() {
        given:
        def token = Mock(OrderCreateToken)
        token.containsContinueType(_) >> true
        def wrapper = WrapperOfApprovalFlowReuse.builder()
                .withOrderCreateToken(token)
                .build()
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.convert(Tuple1.of(wrapper))

        then:
        result.getT1() == false
        result.getT2() == null
    }

    def "check should return null when token not contains APPROVAL_FLOW_REUSE"() {
        given:
        def token = Stub(OrderCreateToken) {
            containsContinueType(_) >> false
        }
        def wrapper = WrapperOfApprovalFlowReuse.builder()
                .withOrderCreateToken(token)
                .withOrderCreateRequestType(Stub(OrderCreateRequestType))
                .withStrategyInfoMap(Stub(Map))
                .withGetOrderFoundationDataResponseType(Stub(GetOrderFoundationDataResponseType))
                .withCheckHotelAuthExtensionResponseType(Stub(CheckHotelAuthExtensionResponseType))
                .build()
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.check(Tuple1.of(wrapper))

        then:
        result == null
    }

    def "check should return null when needCheckHotelAuthExtensionOfAiContinue is false"() {
        given:
        GroovyMock(OrderCreateProcessorOfUtil, global: true)
        OrderCreateProcessorOfUtil.needCheckHotelAuthExtensionOfAiContinue(*_) >> false

        def token = Stub(OrderCreateToken) {
            containsContinueType(_) >> true
        }
        def wrapper = WrapperOfApprovalFlowReuse.builder()
                .withOrderCreateToken(token)
                .withOrderCreateRequestType(Stub(OrderCreateRequestType))
                .withStrategyInfoMap(Stub(Map))
                .withGetOrderFoundationDataResponseType(Stub(GetOrderFoundationDataResponseType))
                .withCheckHotelAuthExtensionResponseType(Stub(CheckHotelAuthExtensionResponseType))
                .build()
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.check(Tuple1.of(wrapper))

        then:
        result == null
    }

    def "check should return null when checkHotelAuthExtensionResponseType.getContinueAuth is true"() {
        given:
        GroovyMock(OrderCreateProcessorOfUtil, global: true)
        OrderCreateProcessorOfUtil.needCheckHotelAuthExtensionOfAiContinue(*_) >> true

        def token = Stub(OrderCreateToken) {
            containsContinueType(_) >> true
        }
        def checkHotelAuth = Stub(CheckHotelAuthExtensionResponseType) {
            getContinueAuth() >> true
        }
        def wrapper = WrapperOfApprovalFlowReuse.builder()
                .withOrderCreateToken(token)
                .withOrderCreateRequestType(Stub(OrderCreateRequestType))
                .withStrategyInfoMap(Stub(Map))
                .withGetOrderFoundationDataResponseType(Stub(GetOrderFoundationDataResponseType))
                .withCheckHotelAuthExtensionResponseType(checkHotelAuth)
                .build()
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.check(Tuple1.of(wrapper))

        then:
        result == null
    }

    def "check 抛出异常 当所有条件都满足且 getContinueAuth 为 false"() {
        given:
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            static boolean needCheckHotelAuthExtensionOfAiContinue(
                    OrderCreateRequestType orderCreateRequestType,
                    Map strategyInfoMap,
                    GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
                    OrderCreateToken orderCreateToken
            ) {
                return true
            }
        }

        def token = Stub(OrderCreateToken) {
            containsContinueType(ContinueTypeConst.APPROVAL_FLOW_REUSE) >> true
        }
        def checkHotelAuth = Stub(CheckHotelAuthExtensionResponseType) {
            getContinueAuth() >> false
        }
        def wrapper = WrapperOfApprovalFlowReuse.builder()
                .withOrderCreateToken(token)
                .withOrderCreateRequestType(Stub(OrderCreateRequestType))
                .withStrategyInfoMap(Stub(Map))
                .withGetOrderFoundationDataResponseType(Stub(GetOrderFoundationDataResponseType))
                .withCheckHotelAuthExtensionResponseType(checkHotelAuth)
                .build()
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        mapper.check(Tuple1.of(wrapper))

        then:
        def e = thrown(BusinessException)
        e.getErrorCode() == OrderCreateErrorEnum.APPROVAL_FLOW_REUSE_AI_CONTINUE_CHECK_ERROR.getErrorCode()
    }

    def "buildApprovalFlowReuseResponseByQuery should return (false, new OrderCreateResponseType) for other cases"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def queryHotelAuth = Stub(QueryHotelAuthExtensionResponseType) {
            getContinueAuth() >> false
            getOriginalOrderInfo() >> null
        }
        def requestType = Stub(OrderCreateRequestType) {
            getApprovalFlowReuseInput() >> null
        }

        when:
        def result = mapper.buildApprovalFlowReuseResponseByQuery(queryHotelAuth, requestType, Stub(GetOrderFoundationDataResponseType), Stub(Map), Stub(OrderCreateToken), Stub(com.ctrip.soa._21234.SearchTripBasicInfoResponseType), Stub(com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailInfoType), Stub(com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity), Stub(com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity), Stub(com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig), Stub(corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType), Stub(corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType))

        then:
        result.getT1() == false
        result.getT2() instanceof OrderCreateResponseType
    }

    def "should call buildApprovalFlowReuseResponseByQueryReuse when can reuse"() {
        given:
        def queryHotelAuth = Stub(QueryHotelAuthExtensionResponseType) {
            getContinueAuth() >> true
            getOriginalOrderInfo() >> Stub(OriginalOrderInfoType) {
                getOriginalOrderId() >> 123L
            }
        }
        def mapper = GroovySpy(MapperOfApprovalFlowReuseResponse, constructorArgs: [])
        mapper.buildApprovalFlowReuseResponseByQueryReuse(_, _, _, _, _) >> Tuple2.of(true, new OrderCreateResponseType())

        when:
        def result = mapper.buildApprovalFlowReuseResponseByQuery(
                queryHotelAuth, Stub(OrderCreateRequestType), Stub(GetOrderFoundationDataResponseType), Stub(Map), Stub(OrderCreateToken),
                Stub(SearchTripBasicInfoResponseType), Stub(OrderDetailInfoType), Stub(CityBaseInfoEntity), Stub(CityBaseInfoEntity),
                Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig),
                Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType)
        )

        then:
        result.getT1() == true
        result.getT2() instanceof OrderCreateResponseType
    }

    def "buildApprovalFlowReuseResponseByQuery - queryHotelAuthExtensionResponseType 为 null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.buildApprovalFlowReuseResponseByQuery(
                null, Stub(OrderCreateRequestType), Stub(GetOrderFoundationDataResponseType), Stub(Map), Stub(OrderCreateToken),
                Stub(SearchTripBasicInfoResponseType), Stub(OrderDetailInfoType), Stub(CityBaseInfoEntity), Stub(CityBaseInfoEntity),
                Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig),
                Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType)
        )

        then:
        result.getT1() == false
        result.getT2() == null
    }

    def "buildApprovalFlowReuseResponseByQuery - 不可沿用但有推荐单据且需要二次提醒"() {
        given:
        def queryHotelAuth = Stub(QueryHotelAuthExtensionResponseType) {
            getContinueAuth() >> false
            getOriginalOrderInfo() >> Stub(OriginalOrderInfoType) {
                getOriginalOrderId() >> 123L
            }
            getReasonList() >> ["reason1"]
        }
        def strategyInfo = Mock(StrategyInfo) {
            getStrategyValue() >> "T"
        }
        def strategyInfoMap = new HashMap<String, StrategyInfo>()
        strategyInfoMap.put("APPROVAL_FLOW_REUSE_AI_MODIFY", strategyInfo)

        GroovyMock(StrategyOfBookingInitUtil, global: true)

        def mapper = GroovySpy(MapperOfApprovalFlowReuseResponse, constructorArgs: [])
        mapper.buildNeedFollowFailRemindSingle(_) >> true
        mapper.buildApprovalFlowReuseResponseByQueryModify(_, _, _, _, _, _, _, _, _, _, _, _) >> Tuple2.of(true, new OrderCreateResponseType())

        when:
        def result = mapper.buildApprovalFlowReuseResponseByQuery(
                queryHotelAuth, Stub(OrderCreateRequestType), Stub(GetOrderFoundationDataResponseType), strategyInfoMap, Stub(OrderCreateToken),
                Stub(SearchTripBasicInfoResponseType), Stub(OrderDetailInfoType), Stub(CityBaseInfoEntity), Stub(CityBaseInfoEntity),
                Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig),
                Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType)
        )

        then:
        result.getT1() == true
        result.getT2() instanceof OrderCreateResponseType
    }

    def "buildApprovalFlowReuseResponseByQuery - 不可沿用但有推荐单据但不需要二次提醒 approvalFlowReuseAiModify为false"() {
        given:
        def queryHotelAuth = Stub(QueryHotelAuthExtensionResponseType) {
            getContinueAuth() >> false
            getOriginalOrderInfo() >> Stub(OriginalOrderInfoType) {
                getOriginalOrderId() >> 123L
            }
            getReasonList() >> ["reason1"]
        }
        def strategyInfoMap = new HashMap<String, StrategyInfo>()

        def mapper = GroovySpy(MapperOfApprovalFlowReuseResponse, constructorArgs: [])
        mapper.buildNeedFollowFailRemindSingle(_) >> true

        when:
        def result = mapper.buildApprovalFlowReuseResponseByQuery(
                queryHotelAuth, Stub(OrderCreateRequestType), Stub(GetOrderFoundationDataResponseType), strategyInfoMap, Stub(OrderCreateToken),
                Stub(SearchTripBasicInfoResponseType), Stub(OrderDetailInfoType), Stub(CityBaseInfoEntity), Stub(CityBaseInfoEntity),
                Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig),
                Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType)
        )

        then:
        result.getT1() == false
        result.getT2() instanceof OrderCreateResponseType
    }

    def "buildApprovalFlowReuseResponseByQuery - 不可沿用但有推荐单据但不需要二次提醒 buildNeedFollowFailRemindSingle为false"() {
        given:
        def queryHotelAuth = Stub(QueryHotelAuthExtensionResponseType) {
            getContinueAuth() >> false
            getOriginalOrderInfo() >> Stub(OriginalOrderInfoType) {
                getOriginalOrderId() >> 123L
            }
            getReasonList() >> ["reason1"]
        }
        def strategyInfo = Mock(StrategyInfo) {
            getStrategyValue() >> "T"
        }
        def strategyInfoMap = new HashMap<String, StrategyInfo>()
        strategyInfoMap.put("APPROVAL_FLOW_REUSE_AI_MODIFY", strategyInfo)

        def mapper = GroovySpy(MapperOfApprovalFlowReuseResponse, constructorArgs: [])
        mapper.buildNeedFollowFailRemindSingle(_) >> false

        when:
        def result = mapper.buildApprovalFlowReuseResponseByQuery(
                queryHotelAuth, Stub(OrderCreateRequestType), Stub(GetOrderFoundationDataResponseType), strategyInfoMap, Stub(OrderCreateToken),
                Stub(SearchTripBasicInfoResponseType), Stub(OrderDetailInfoType), Stub(CityBaseInfoEntity), Stub(CityBaseInfoEntity),
                Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig),
                Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType)
        )

        then:

        result.getT1() == true
        result.getT2() instanceof OrderCreateResponseType
    }



    def "buildApprovalFlowReuseResponseByQuery - 其他情况"() {
        given:
        def queryHotelAuth = Stub(QueryHotelAuthExtensionResponseType) {
            getContinueAuth() >> false
            getOriginalOrderInfo() >> null
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getApprovalFlowReuseInput() >> null
        }
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.buildApprovalFlowReuseResponseByQuery(
                queryHotelAuth, orderCreateRequestType, Stub(GetOrderFoundationDataResponseType), Stub(Map), Stub(OrderCreateToken),
                Stub(SearchTripBasicInfoResponseType), Stub(OrderDetailInfoType), Stub(CityBaseInfoEntity), Stub(CityBaseInfoEntity),
                Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig),
                Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType)
        )

        then:
        result.getT1() == false
        result.getT2() instanceof OrderCreateResponseType
    }

    def "getOrderCreateTokenModify 应该添加APPROVAL_FLOW_REUSE并设置canFollowApproval为false"() {
        given:
        def token = Mock(OrderCreateToken)
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.getOrderCreateTokenModify(token)

        then:
        1 * token.addContinueTypes(ContinueTypeConst.APPROVAL_FLOW_REUSE)
        result == token
    }


    def "buildNeedFollowFailRemindSingle 应该返回 false 当 reasonList 为 null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.buildNeedFollowFailRemindSingle(null)

        then:
        !result
    }

    def "buildNeedFollowFailRemindSingle 应该返回 false 当 reasonList 为空列表"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def reasonList = new ArrayList<String>()

        when:
        def result = mapper.buildNeedFollowFailRemindSingle(reasonList)

        then:
        !result
    }

    def "buildNeedFollowFailRemindSingle 应该返回 false 当 reasonList 包含 cannotFlowAndNoNeedEndReasonCodes 匹配的元素"() {
        given:
        new MockUp<QconfigOfInitConfig>() {
            @Mock
            static boolean cannotFlowAndNoNeedEndReasonCodes(String reasonCode) {
                return "SPECIAL_REASON_CODE".equals(reasonCode)
            }
        }
        
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def reasonList = ["NORMAL_REASON", "SPECIAL_REASON_CODE", "ANOTHER_REASON"]

        when:
        def result = mapper.buildNeedFollowFailRemindSingle(reasonList)

        then:
        !result
    }

    def "buildNeedFollowFailRemindSingle 应该返回 false 当 reasonList 的第一个元素匹配 cannotFlowAndNoNeedEndReasonCodes"() {
        given:
        new MockUp<QconfigOfInitConfig>() {
            @Mock
            static boolean cannotFlowAndNoNeedEndReasonCodes(String reasonCode) {
                return "FIRST_SPECIAL_CODE".equals(reasonCode)
            }
        }
        
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def reasonList = ["FIRST_SPECIAL_CODE", "NORMAL_REASON", "ANOTHER_REASON"]

        when:
        def result = mapper.buildNeedFollowFailRemindSingle(reasonList)

        then:
        !result
    }

    def "buildNeedFollowFailRemindSingle 应该返回 false 当 reasonList 的最后一个元素匹配 cannotFlowAndNoNeedEndReasonCodes"() {
        given:
        new MockUp<QconfigOfInitConfig>() {
            @Mock
            static boolean cannotFlowAndNoNeedEndReasonCodes(String reasonCode) {
                return "LAST_SPECIAL_CODE".equals(reasonCode)
            }
        }
        
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def reasonList = ["NORMAL_REASON", "ANOTHER_REASON", "LAST_SPECIAL_CODE"]

        when:
        def result = mapper.buildNeedFollowFailRemindSingle(reasonList)

        then:
        !result
    }

    def "buildNeedFollowFailRemindSingle 应该返回 true 当 reasonList 不包含任何匹配 cannotFlowAndNoNeedEndReasonCodes 的元素"() {
        given:
        new MockUp<QconfigOfInitConfig>() {
            @Mock
            static boolean cannotFlowAndNoNeedEndReasonCodes(String reasonCode) {
                return false
            }
        }
        
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def reasonList = ["NORMAL_REASON_1", "NORMAL_REASON_2", "NORMAL_REASON_3"]

        when:
        def result = mapper.buildNeedFollowFailRemindSingle(reasonList)

        then:
        result
    }

    def "buildNeedFollowFailRemindSingle 应该返回 true 当 reasonList 只有一个元素且不匹配 cannotFlowAndNoNeedEndReasonCodes"() {
        given:
        // Mock QconfigOfInitConfig.cannotFlowAndNoNeedEndReasonCodes 静态方法
        new MockUp<QconfigOfInitConfig>() {
            @Mock
            static boolean cannotFlowAndNoNeedEndReasonCodes(String reasonCode) {
                return false
            }
        }
        
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def reasonList = ["SINGLE_NORMAL_REASON"]

        when:
        def result = mapper.buildNeedFollowFailRemindSingle(reasonList)

        then:
        result
    }

    def "buildNeedFollowFailRemindSingle 应该正确处理空字符串元素"() {
        given:
        new MockUp<QconfigOfInitConfig>() {
            @Mock
            static boolean cannotFlowAndNoNeedEndReasonCodes(String reasonCode) {
                return "".equals(reasonCode)
            }
        }
        
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def reasonList = ["NORMAL_REASON", "", "ANOTHER_REASON"]

        when:
        def result = mapper.buildNeedFollowFailRemindSingle(reasonList)

        then:
        !result
    }

    def "buildNeedFollowFailRemindSingle 应该正确处理包含null元素的列表"() {
        given:
        new MockUp<QconfigOfInitConfig>() {
            @Mock
            static boolean cannotFlowAndNoNeedEndReasonCodes(String reasonCode) {
                return reasonCode == null // null匹配
            }
        }
        
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def reasonList = ["NORMAL_REASON", null, "ANOTHER_REASON"]

        when:
        def result = mapper.buildNeedFollowFailRemindSingle(reasonList)

        then:
        !result
    }

    def "buildApprovalFlowReuseResponseByQueryNoReuse 应该正确构建响应并返回 Tuple2"() {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            static String generateToken(Object token, Class clazz) {
                return "mocked-token"
            }
        }

        def mapper = new MapperOfApprovalFlowReuseResponse()
        def mockOrderCreateToken = Stub(OrderCreateToken)

        def spyMapper = Spy(mapper)
        spyMapper.getOrderCreateTokenModify(_) >> mockOrderCreateToken

        def queryHotelAuthExtensionResponseType = Stub(QueryHotelAuthExtensionResponseType) {
            getReasonList() >> []
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType)
        def orderCreateToken = Stub(OrderCreateToken)
        def searchTripBasicInfoResponseTypeOfApprovalFlowReuse = Stub(SearchTripBasicInfoResponseType)
        def orderDetailInfoTypeOfApprovalFlowReuse = Stub(OrderDetailInfoType)
        def cityBaseInfoEntityOfApprovalFlowReuse = Stub(CityBaseInfoEntity)
        def cityBaseInfoEntity = Stub(CityBaseInfoEntity)
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo)
        def qconfigOfCertificateInitConfig = Stub(QconfigOfCertificateInitConfig)
        def policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse = Stub(GetCorpUserInfoResponseType)
        def policyGetCorpUserInfoResponseType = Stub(GetCorpUserInfoResponseType)

        when:
        def method = MapperOfApprovalFlowReuseResponse.class.getDeclaredMethod(
                "buildApprovalFlowReuseResponseByQueryNoReuse",
                QueryHotelAuthExtensionResponseType.class,
                OrderCreateRequestType.class,
                OrderCreateToken.class,
                SearchTripBasicInfoResponseType.class,
                OrderDetailInfoType.class,
                CityBaseInfoEntity.class,
                CityBaseInfoEntity.class,
                WrapperOfCheckAvail.BaseCheckAvailInfo.class,
                QconfigOfCertificateInitConfig.class,
                GetCorpUserInfoResponseType.class,
                GetCorpUserInfoResponseType.class
        )
        method.setAccessible(true)
        def result = method.invoke(spyMapper,
                queryHotelAuthExtensionResponseType,
                orderCreateRequestType,
                orderCreateToken,
                searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
                orderDetailInfoTypeOfApprovalFlowReuse,
                cityBaseInfoEntityOfApprovalFlowReuse,
                cityBaseInfoEntity,
                checkAvailInfo,
                qconfigOfCertificateInitConfig,
                policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
                policyGetCorpUserInfoResponseType
        )

        then:
        result.getT1() == true
        result.getT2() instanceof OrderCreateResponseType
        result.getT2().getOrderCreateToken() == "mocked-token"
        result.getT2().getApprovalFlowReuse() instanceof ApprovalFlowReuse
        result.getT2().getApprovalFlowReuse().getApprovalFlowReuseInfo() != null
        result.getT2().getApprovalFlowReuse().getHotelReuseOrderInfo() != null
    }

    def "buildApprovalFlowReuseResponseByQueryNoReuse 应该正确处理所有参数为null的情况"() {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            static String generateToken(Object token, Class clazz) {
                return "mocked-token"
            }
        }

        def mapper = new MapperOfApprovalFlowReuseResponse()
        def mockOrderCreateToken = Stub(OrderCreateToken)

        def spyMapper = Spy(mapper)
        spyMapper.getOrderCreateTokenModify(_) >> mockOrderCreateToken

        when:
        def method = MapperOfApprovalFlowReuseResponse.class.getDeclaredMethod(
                "buildApprovalFlowReuseResponseByQueryNoReuse",
                QueryHotelAuthExtensionResponseType.class,
                OrderCreateRequestType.class,
                OrderCreateToken.class,
                SearchTripBasicInfoResponseType.class,
                OrderDetailInfoType.class,
                CityBaseInfoEntity.class,
                CityBaseInfoEntity.class,
                WrapperOfCheckAvail.BaseCheckAvailInfo.class,
                QconfigOfCertificateInitConfig.class,
                GetCorpUserInfoResponseType.class,
                GetCorpUserInfoResponseType.class
        )
        method.setAccessible(true)
        def result = method.invoke(spyMapper,
                null, // queryHotelAuthExtensionResponseType
                null, // orderCreateRequestType
                null, // orderCreateToken
                null, // searchTripBasicInfoResponseTypeOfApprovalFlowReuse
                null, // orderDetailInfoTypeOfApprovalFlowReuse
                null, // cityBaseInfoEntityOfApprovalFlowReuse
                null, // cityBaseInfoEntity
                null, // checkAvailInfo
                null, // qconfigOfCertificateInitConfig
                null, // policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse
                null  // policyGetCorpUserInfoResponseType
        )

        then:
        def e = thrown(Exception)
        e.cause instanceof NullPointerException
    }

    def "buildApprovalFlowReuseResponseByQueryNoReuse 应该正确处理TokenParseUtil.generateToken抛出异常的情况"() {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            static String generateToken(Object token, Class clazz) {
                throw new RuntimeException("Token generation failed")
            }
        }

        def mapper = new MapperOfApprovalFlowReuseResponse()

        def mockOrderCreateToken = Stub(OrderCreateToken)

        def spyMapper = Spy(mapper)
        spyMapper.getOrderCreateTokenModify(_) >> mockOrderCreateToken

        def queryHotelAuthExtensionResponseType = Stub(QueryHotelAuthExtensionResponseType)
        def orderCreateRequestType = Stub(OrderCreateRequestType)
        def orderCreateToken = Stub(OrderCreateToken)
        def searchTripBasicInfoResponseTypeOfApprovalFlowReuse = Stub(SearchTripBasicInfoResponseType)
        def orderDetailInfoTypeOfApprovalFlowReuse = Stub(OrderDetailInfoType)
        def cityBaseInfoEntityOfApprovalFlowReuse = Stub(CityBaseInfoEntity)
        def cityBaseInfoEntity = Stub(CityBaseInfoEntity)
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo)
        def qconfigOfCertificateInitConfig = Stub(QconfigOfCertificateInitConfig)
        def policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse = Stub(GetCorpUserInfoResponseType)
        def policyGetCorpUserInfoResponseType = Stub(GetCorpUserInfoResponseType)

        when:
        def method = MapperOfApprovalFlowReuseResponse.class.getDeclaredMethod(
                "buildApprovalFlowReuseResponseByQueryNoReuse",
                QueryHotelAuthExtensionResponseType.class,
                OrderCreateRequestType.class,
                OrderCreateToken.class,
                SearchTripBasicInfoResponseType.class,
                OrderDetailInfoType.class,
                CityBaseInfoEntity.class,
                CityBaseInfoEntity.class,
                WrapperOfCheckAvail.BaseCheckAvailInfo.class,
                QconfigOfCertificateInitConfig.class,
                GetCorpUserInfoResponseType.class,
                GetCorpUserInfoResponseType.class
        )
        method.setAccessible(true)
        method.invoke(spyMapper,
                queryHotelAuthExtensionResponseType,
                orderCreateRequestType,
                orderCreateToken,
                searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
                orderDetailInfoTypeOfApprovalFlowReuse,
                cityBaseInfoEntityOfApprovalFlowReuse,
                cityBaseInfoEntity,
                checkAvailInfo,
                qconfigOfCertificateInitConfig,
                policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
                policyGetCorpUserInfoResponseType
        )

        then:
        def e = thrown(Exception)
        e.cause instanceof RuntimeException
        e.cause.message == "Token generation failed"
    }

    def "buildApprovalFlowReuseResponseByQueryNoReuse 应该验证OrderCreateResponseType的所有属性设置"() {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            static String generateToken(Object token, Class clazz) {
                return "test-token-123"
            }
        }

        def mapper = new MapperOfApprovalFlowReuseResponse()
        def mockOrderCreateToken = Stub(OrderCreateToken)

        def spyMapper = Spy(mapper)
        spyMapper.getOrderCreateTokenModify(_) >> mockOrderCreateToken

        def queryHotelAuthExtensionResponseType = Stub(QueryHotelAuthExtensionResponseType) {
            getReasonList() >> []
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType)
        def orderCreateToken = Stub(OrderCreateToken)
        def searchTripBasicInfoResponseTypeOfApprovalFlowReuse = Stub(SearchTripBasicInfoResponseType)
        def orderDetailInfoTypeOfApprovalFlowReuse = Stub(OrderDetailInfoType)
        def cityBaseInfoEntityOfApprovalFlowReuse = Stub(CityBaseInfoEntity)
        def cityBaseInfoEntity = Stub(CityBaseInfoEntity)
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo)
        def qconfigOfCertificateInitConfig = Stub(QconfigOfCertificateInitConfig)
        def policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse = Stub(GetCorpUserInfoResponseType)
        def policyGetCorpUserInfoResponseType = Stub(GetCorpUserInfoResponseType)

        when:
        def method = MapperOfApprovalFlowReuseResponse.class.getDeclaredMethod(
                "buildApprovalFlowReuseResponseByQueryNoReuse",
                QueryHotelAuthExtensionResponseType.class,
                OrderCreateRequestType.class,
                OrderCreateToken.class,
                SearchTripBasicInfoResponseType.class,
                OrderDetailInfoType.class,
                CityBaseInfoEntity.class,
                CityBaseInfoEntity.class,
                WrapperOfCheckAvail.BaseCheckAvailInfo.class,
                QconfigOfCertificateInitConfig.class,
                GetCorpUserInfoResponseType.class,
                GetCorpUserInfoResponseType.class
        )
        method.setAccessible(true)
        def result = method.invoke(spyMapper,
                queryHotelAuthExtensionResponseType,
                orderCreateRequestType,
                orderCreateToken,
                searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
                orderDetailInfoTypeOfApprovalFlowReuse,
                cityBaseInfoEntityOfApprovalFlowReuse,
                cityBaseInfoEntity,
                checkAvailInfo,
                qconfigOfCertificateInitConfig,
                policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
                policyGetCorpUserInfoResponseType
        )

        then:
        result.getT1() == true
        result.getT2() instanceof OrderCreateResponseType
        result.getT2().getOrderCreateToken() == "test-token-123"
        result.getT2().getApprovalFlowReuse() instanceof ApprovalFlowReuse
        result.getT2().getApprovalFlowReuse().getApprovalFlowReuseInfo() != null
        result.getT2().getApprovalFlowReuse().getHotelReuseOrderInfo() != null
    }


    def "buildApprovalFlowReuseResponseByQueryModify 所有参数为null"() {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            static String generateToken(Object token, Class clazz) {
                return "mocked-token"
            }
        }
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def spyMapper = Spy(mapper)

        when:
        def method = MapperOfApprovalFlowReuseResponse.class.getDeclaredMethod(
                "buildApprovalFlowReuseResponseByQueryModify",
                QueryHotelAuthExtensionResponseType.class,
                OrderCreateRequestType.class,
                OrderCreateToken.class,
                SearchTripBasicInfoResponseType.class,
                OrderDetailInfoType.class,
                CityBaseInfoEntity.class,
                CityBaseInfoEntity.class,
                WrapperOfCheckAvail.BaseCheckAvailInfo.class,
                QconfigOfCertificateInitConfig.class,
                GetCorpUserInfoResponseType.class,
                GetCorpUserInfoResponseType.class
        )
        method.setAccessible(true)
        method.invoke(spyMapper,
                null, null, null, null, null, null, null, null, null, null, null
        )

        then:
        def e = thrown(Exception)
        e.cause instanceof NullPointerException
    }

    def "buildApprovalFlowReuseResponseByQueryModify 正常流程"() {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            static String generateToken(Object token, Class clazz) {
                return "mocked-token"
            }
        }
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def spyMapper = Spy(mapper)
        def mockOrderCreateToken = Stub(OrderCreateToken)
        def mockApprovalFlowReuse = Stub(ApprovalFlowReuse)
        spyMapper.getOrderCreateTokenModify(_) >> mockOrderCreateToken
        spyMapper.buildApprovalFlowReuseByQueryModify(_, _, _, _, _, _, _, _, _, _, _) >> mockApprovalFlowReuse

        def queryHotelAuthExtensionResponseType = Stub(QueryHotelAuthExtensionResponseType)
        def orderCreateRequestType = Stub(OrderCreateRequestType)
        def orderCreateToken = Stub(OrderCreateToken)
        def searchTripBasicInfoResponseTypeOfApprovalFlowReuse = Stub(SearchTripBasicInfoResponseType)
        def orderDetailInfoTypeOfApprovalFlowReuse = Stub(OrderDetailInfoType)
        def cityBaseInfoEntityOfApprovalFlowReuse = Stub(CityBaseInfoEntity)
        def cityBaseInfoEntity = Stub(CityBaseInfoEntity)
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo)
        def qconfigOfCertificateInitConfig = Stub(QconfigOfCertificateInitConfig)
        def policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse = Stub(GetCorpUserInfoResponseType)
        def policyGetCorpUserInfoResponseType = Stub(GetCorpUserInfoResponseType)

        when:
        def method = MapperOfApprovalFlowReuseResponse.class.getDeclaredMethod(
                "buildApprovalFlowReuseResponseByQueryModify",
                QueryHotelAuthExtensionResponseType.class,
                OrderCreateRequestType.class,
                OrderCreateToken.class,
                SearchTripBasicInfoResponseType.class,
                OrderDetailInfoType.class,
                CityBaseInfoEntity.class,
                CityBaseInfoEntity.class,
                WrapperOfCheckAvail.BaseCheckAvailInfo.class,
                QconfigOfCertificateInitConfig.class,
                GetCorpUserInfoResponseType.class,
                GetCorpUserInfoResponseType.class
        )
        method.setAccessible(true)
        def result = method.invoke(spyMapper,
                queryHotelAuthExtensionResponseType,
                orderCreateRequestType,
                orderCreateToken,
                searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
                orderDetailInfoTypeOfApprovalFlowReuse,
                cityBaseInfoEntityOfApprovalFlowReuse,
                cityBaseInfoEntity,
                checkAvailInfo,
                qconfigOfCertificateInitConfig,
                policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
                policyGetCorpUserInfoResponseType
        )

        then:
        result.getT1() == true
        result.getT2() instanceof OrderCreateResponseType
        result.getT2().getOrderCreateToken() == "mocked-token"
        result.getT2().getApprovalFlowReuse() != null
        result.getT2().getApprovalFlowReuse() instanceof ApprovalFlowReuse
        result.getT2().getApprovalFlowReuse().approvalFlowReuseInfo?.reuseType == "AI_BOOK_REUSE"
        result.getT2().getApprovalFlowReuse().approvalFlowReuseInfo?.reuseStatus == "MODIFY_REUSE"
    }

    def "buildApprovalFlowReuseResponseByQueryModify TokenParseUtil.generateToken抛异常"() {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            static String generateToken(Object token, Class clazz) {
                throw new RuntimeException("token error")
            }
        }
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def spyMapper = Spy(mapper)
        spyMapper.getOrderCreateTokenModify(_) >> Stub(OrderCreateToken)
        spyMapper.buildApprovalFlowReuseByQueryModify(_, _, _, _, _, _, _, _, _, _, _) >> Stub(ApprovalFlowReuse)

        when:
        def method = MapperOfApprovalFlowReuseResponse.class.getDeclaredMethod(
                "buildApprovalFlowReuseResponseByQueryModify",
                QueryHotelAuthExtensionResponseType.class,
                OrderCreateRequestType.class,
                OrderCreateToken.class,
                SearchTripBasicInfoResponseType.class,
                OrderDetailInfoType.class,
                CityBaseInfoEntity.class,
                CityBaseInfoEntity.class,
                WrapperOfCheckAvail.BaseCheckAvailInfo.class,
                QconfigOfCertificateInitConfig.class,
                GetCorpUserInfoResponseType.class,
                GetCorpUserInfoResponseType.class
        )
        method.setAccessible(true)
        method.invoke(spyMapper,
                Stub(QueryHotelAuthExtensionResponseType),
                Stub(OrderCreateRequestType),
                Stub(OrderCreateToken),
                Stub(SearchTripBasicInfoResponseType),
                Stub(OrderDetailInfoType),
                Stub(CityBaseInfoEntity),
                Stub(CityBaseInfoEntity),
                Stub(WrapperOfCheckAvail.BaseCheckAvailInfo),
                Stub(QconfigOfCertificateInitConfig),
                Stub(GetCorpUserInfoResponseType),
                Stub(GetCorpUserInfoResponseType)
        )

        then:
        def e = thrown(Exception)
        e.cause instanceof RuntimeException
        e.cause.message == "token error"
    }

    def "buildApprovalFlowReuseResponseByQueryModify getOrderCreateTokenModify返回null"() {
        given:
        new MockUp<TokenParseUtil>() {
            @mockit.Mock
            static String generateToken(Object token, Class clazz) {
                return "mocked-token"
            }
        }
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def spyMapper = Spy(mapper)
        spyMapper.getOrderCreateTokenModify(_) >> null
        spyMapper.buildApprovalFlowReuseByQueryModify(_, _, _, _, _, _, _, _, _, _, _) >> Stub(ApprovalFlowReuse)

        when:
        def method = MapperOfApprovalFlowReuseResponse.class.getDeclaredMethod(
                "buildApprovalFlowReuseResponseByQueryModify",
                QueryHotelAuthExtensionResponseType.class,
                OrderCreateRequestType.class,
                OrderCreateToken.class,
                SearchTripBasicInfoResponseType.class,
                OrderDetailInfoType.class,
                CityBaseInfoEntity.class,
                CityBaseInfoEntity.class,
                WrapperOfCheckAvail.BaseCheckAvailInfo.class,
                QconfigOfCertificateInitConfig.class,
                GetCorpUserInfoResponseType.class,
                GetCorpUserInfoResponseType.class
        )
        method.setAccessible(true)
        def result = method.invoke(spyMapper,
                Stub(QueryHotelAuthExtensionResponseType),
                Stub(OrderCreateRequestType),
                Stub(OrderCreateToken),
                Stub(SearchTripBasicInfoResponseType),
                Stub(OrderDetailInfoType),
                Stub(CityBaseInfoEntity),
                Stub(CityBaseInfoEntity),
                Stub(WrapperOfCheckAvail.BaseCheckAvailInfo),
                Stub(QconfigOfCertificateInitConfig),
                Stub(GetCorpUserInfoResponseType),
                Stub(GetCorpUserInfoResponseType)
        )

        then:
        result.getT2().getOrderCreateToken() == "mocked-token"
    }


    def "getOrderCreateTokenReuseByCheck tripId不为0且不为null，走if分支"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def orderCreateToken = Mock(OrderCreateToken)
        def originalOrderInfo = Mock(OriginalOrderInfoType) {
            getTripId() >> 123L
            getOriginalOrderId() >> 456L
        }
        def queryHotelAuthExtensionResponseType = Mock(QueryHotelAuthExtensionResponseType) {
            getOriginalOrderInfo() >> originalOrderInfo
        }
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isNotZeroAndNull(123L) >> true

        when:
        def result = mapper.getOrderCreateTokenReuseByCheck(orderCreateToken, queryHotelAuthExtensionResponseType)

        then:
        1 * orderCreateToken.addContinueTypes(ContinueTypeConst.APPROVAL_FLOW_REUSE)
        1 * orderCreateToken.setFollowApprovalResult(_ as FollowApprovalResult)
        result == orderCreateToken
    }

    def "getOrderCreateTokenReuseByCheck tripId为0或null，不走if分支"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def orderCreateToken = Mock(OrderCreateToken)
        def originalOrderInfo = Mock(OriginalOrderInfoType) {
            getTripId() >> null
            getOriginalOrderId() >> 789L
        }
        def queryHotelAuthExtensionResponseType = Mock(QueryHotelAuthExtensionResponseType) {
            getOriginalOrderInfo() >> originalOrderInfo
        }
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isNotZeroAndNull(null) >> false

        when:
        def result = mapper.getOrderCreateTokenReuseByCheck(orderCreateToken, queryHotelAuthExtensionResponseType)

        then:
        1 * orderCreateToken.addContinueTypes(ContinueTypeConst.APPROVAL_FLOW_REUSE)
        1 * orderCreateToken.setFollowApprovalResult(_ as FollowApprovalResult)
        result == orderCreateToken
    }


    def "buildReuseType - buildApprovalReuseReBookOrderId为非零非空，返回RE_BOOK_REUSE"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def orderCreateRequestType = Stub(OrderCreateRequestType)
        def strategyInfoMap = new HashMap<String, StrategyInfo>()
        def getOrderFoundationDataResponseType = Stub(GetOrderFoundationDataResponseType)
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            static boolean approvalReuseReBook(Map map) { return true }
        }
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            static Long buildApprovalReuseReBookOrderId(Object a, Object b) { return 123L }
        }
        new MockUp<TemplateNumberUtil>() {
            @Mock
            static boolean isNotZeroAndNull(Long v) { return true }
        }

        when:
        def result = mapper.buildReuseType(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType)

        then:
        result == "RE_BOOK_REUSE"
    }

    def "buildReuseType - buildApprovalReuseReBookOrderId为零或空，buildArtificialReuseNoOrderId和buildArtificialReuseNoTripId都为零或空，返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def orderCreateRequestType = Stub(OrderCreateRequestType)
        def strategyInfoMap = new HashMap<String, StrategyInfo>()
        def getOrderFoundationDataResponseType = Stub(GetOrderFoundationDataResponseType)

        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            static Long buildApprovalReuseReBookOrderId(Object a, Object b) { return null }
            @Mock
            static Long buildArtificialReuseNoOrderId(Object a, Object b) { return null }
            @Mock
            static Long buildArtificialReuseNoTripId(Object a, Object b) { return null }
        }
        new MockUp<TemplateNumberUtil>() {
            @Mock
            static boolean isNotZeroAndNull(Long v) { return false }
        }

        when:
        def result = mapper.buildReuseType(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType)

        then:
        result == null
    }

    def "buildReuseType - buildApprovalReuseReBookOrderId为0，buildArtificialReuseNoOrderId为0，buildArtificialReuseNoTripId为0，返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def orderCreateRequestType = Stub(OrderCreateRequestType)
        def strategyInfoMap = new HashMap<String, StrategyInfo>()
        def getOrderFoundationDataResponseType = Stub(GetOrderFoundationDataResponseType)

        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            static Long buildApprovalReuseReBookOrderId(Object a, Object b) { return 0L }
            @Mock
            static Long buildArtificialReuseNoOrderId(Object a, Object b) { return 0L }
            @Mock
            static Long buildArtificialReuseNoTripId(Object a, Object b) { return 0L }
        }
        new MockUp<TemplateNumberUtil>() {
            @Mock
            static boolean isNotZeroAndNull(Long v) { return false }
        }

        when:
        def result = mapper.buildReuseType(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType)

        then:
        result == null
    }

    def "reuseResultDetails - reasonList为null，返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.reuseResultDetails(null)

        then:
        result == null
    }

    def "reuseResultDetails - reasonList为空，返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.reuseResultDetails([])

        then:
        result == null
    }

    def "reuseResultDetails - reasonList包含空字符串，跳过该元素"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.reuseResultDetails(["", "VALID_CODE"])

        then:
        result.size() == 1
        result[0] instanceof ReuseResultDetail
    }

    def "reuseResultDetails - reasonList元素找不到FailCodeEnum，跳过该元素"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        new MockUp<BFFSharkUtil>() {
            @Mock
            static String getSharkValue(String key) { return "desc" }
        }
        new MockUp<StringUtil>() {
            @Mock
            static boolean isBlank(String s) { return s == null || s.trim() == "" }
            @Mock
            static String indexedFormat(String template, Object... args) { return "key" }
        }

        when:
        def result = mapper.reuseResultDetails(["NOT_FOUND", "030"]) // "NOT_FOUND"会被映射为UNKNOWN, "030"为PRICE_EXCEEDS_STANDARD

        then:
        result.size() == 2
        result[0].code == "UNKNOWN"
        result[1].code == "PRICE_EXCEEDS_STANDARD"
    }

    def "reuseResultDetails - reasonList全为有效code，全部生成ReuseResultDetail"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        new MockUp<BFFSharkUtil>() {
            @Mock
            static String getSharkValue(String key) { return "desc" }
        }
        new MockUp<StringUtil>() {
            @Mock
            static boolean isBlank(String s) { return s == null || s.trim() == "" }
            @Mock
            static String indexedFormat(String template, Object... args) { return "key" }
        }

        when:
        def result = mapper.reuseResultDetails(["030", "001"]) // "030"=PRICE_EXCEEDS_STANDARD, "001"=ORDER_DATA_EMPTY

        then:
        result.size() == 2
        result[0].code == "PRICE_EXCEEDS_STANDARD"
        result[1].code == "ORDER_DATA_EMPTY"
    }

    def "buildReuseResultInfos - reasonList为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.buildReuseResultInfos(null, Stub(OrderCreateRequestType), Stub(CityBaseInfoEntity), Stub(CityBaseInfoEntity),
                Stub(OrderDetailInfoType), Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig),
                Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType))

        then:
        result == null
    }

    def "buildReuseResultInfos - reasonList为空返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.buildReuseResultInfos([], Stub(OrderCreateRequestType), Stub(CityBaseInfoEntity), Stub(CityBaseInfoEntity),
                Stub(OrderDetailInfoType), Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig),
                Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType))

        then:
        result == null
    }

    def "buildReuseResultInfos - orderDetailInfoTypeOfApprovalFlowReuse为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.buildReuseResultInfos(["CITY_ID_DIFFERENT"], Stub(OrderCreateRequestType), Stub(CityBaseInfoEntity), Stub(CityBaseInfoEntity),
                null, Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig),
                Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType))

        then:
        result == null
    }

    def "buildReuseResultInfos - reason为null或空字符串跳过"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        mapper.metaClass.static.getProperty = { String name ->
            if (name == "COMPARE_WITH_REUSE_ORDER") return ["ANY"]
            return MapperOfApprovalFlowReuseResponse.metaClass.getProperty(name)
        }

        when:
        def result = mapper.buildReuseResultInfos([null, ""], Stub(OrderCreateRequestType), Stub(CityBaseInfoEntity), Stub(CityBaseInfoEntity),
                Stub(OrderDetailInfoType), Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig),
                Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType))

        then:
        result.isEmpty()
    }

    def "buildReuseResultInfos - reason不在COMPARE_WITH_REUSE_ORDER跳过"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        mapper.metaClass.static.getProperty = { String name ->
            if (name == "COMPARE_WITH_REUSE_ORDER") return ["CITY_ID_DIFFERENT"]
            return MapperOfApprovalFlowReuseResponse.metaClass.getProperty(name)
        }

        when:
        def result = mapper.buildReuseResultInfos(["NOT_IN"], Stub(OrderCreateRequestType), Stub(CityBaseInfoEntity), Stub(CityBaseInfoEntity),
                Stub(OrderDetailInfoType), Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig),
                Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType))

        then:
        result.isEmpty()
    }

    def "addReuseResultInfoOfOrderAmount - paymentInfo为null直接return"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def orderDetail = Stub(OrderDetailInfoType) {
            getPaymentInfo() >> null
        }
        def list = []
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo)

        when:
        mapper.addReuseResultInfoOfOrderAmount(orderDetail, list, checkAvailInfo, "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfOrderAmount - customAmountNewBook为blank直接return"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def paymentInfo = Stub(PaymentInfoType) {
            getSettlementOrderAmount() >> new BigDecimal("100")
            getSettlementCurrency() >> "CNY"
        }
        def orderDetail = Stub(OrderDetailInfoType) {
            getPaymentInfo() >> paymentInfo
        }
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getCustomCurrency() >> ""
            getRoomAmount() >> new BigDecimal("200")
        }
        def list = []

        when:
        mapper.addReuseResultInfoOfOrderAmount(orderDetail, list, checkAvailInfo, "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfOrderAmount - settlementCurrencyOfReuseOrder为blank直接return"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def paymentInfo = Stub(PaymentInfoType) {
            getSettlementOrderAmount() >> new BigDecimal("100")
            getSettlementCurrency() >> ""
        }
        def orderDetail = Stub(OrderDetailInfoType) {
            getPaymentInfo() >> paymentInfo
        }
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getCustomCurrency() >> "CNY"
            getRoomAmount() >> new BigDecimal("200")
        }
        def list = []

        when:
        mapper.addReuseResultInfoOfOrderAmount(orderDetail, list, checkAvailInfo, "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfOrderAmount - customCurrencyNewBook为null直接return"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def paymentInfo = Stub(PaymentInfoType) {
            getSettlementOrderAmount() >> new BigDecimal("100")
            getSettlementCurrency() >> "CNY"
        }
        def orderDetail = Stub(OrderDetailInfoType) {
            getPaymentInfo() >> paymentInfo
        }
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getCustomCurrency() >> "CNY"
            getRoomAmount() >> null
        }
        def list = []

        when:
        mapper.addReuseResultInfoOfOrderAmount(orderDetail, list, checkAvailInfo, "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfOrderAmount - settlementOrderAmountOfReuseOrder为null直接return"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def paymentInfo = Stub(PaymentInfoType) {
            getSettlementOrderAmount() >> null
            getSettlementCurrency() >> "CNY"
        }
        def orderDetail = Stub(OrderDetailInfoType) {
            getPaymentInfo() >> paymentInfo
        }
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getCustomCurrency() >> "CNY"
            getRoomAmount() >> new BigDecimal("200")
        }
        def list = []

        when:
        mapper.addReuseResultInfoOfOrderAmount(orderDetail, list, checkAvailInfo, "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfOrderAmount - 正常分支"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def paymentInfo = Stub(PaymentInfoType) {
            getSettlementOrderAmount() >> new BigDecimal("100")
            getSettlementCurrency() >> "CNY"
        }
        def orderDetail = Stub(OrderDetailInfoType) {
            getPaymentInfo() >> paymentInfo
        }
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getCustomCurrency() >> "CNY"
            getRoomAmount() >> new BigDecimal("200")
        }
        def list = []
        mapper.formatAmount(_, _) >> { amount, currency -> "${amount}-${currency}" }
        mapper.buildCheckResultTitleSharkKey(_) >> "sharkKey"

        when:
        mapper.addReuseResultInfoOfOrderAmount(orderDetail, list, checkAvailInfo, "TYPE")

        then:
        list.size() == 1
        list[0] instanceof ReuseResultInfo
        list[0].orderValue == "100-CNY"
        list[0].newBookValue == "200-CNY"
        list[0].checkResultType == "TYPE"
    }

    def "formatAmount - currency为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.formatAmount(new BigDecimal("123.45"), null)

        then:
        result == null
    }

    def "formatAmount - currency为空字符串返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.formatAmount(new BigDecimal("123.45"), "")

        then:
        result == null
    }

    def "formatAmount - amount为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.formatAmount(null, "CNY")

        then:
        result == null
    }

    def "formatAmount - 正常分支返回currencyString结果"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def amount = new BigDecimal("123.45")
        def currency = "CNY"

        when:
        def result = mapper.formatAmount(amount, currency)

        then:
        result == "MOCKED"
    }

    def "convertAmount - amount为null返回null"() {
        when:
        def result = MapperOfApprovalFlowReuseResponse.convertAmount(null, "CNY")

        then:
        result == null
    }

    def "convertAmount - currency为null返回null"() {
        when:
        def result = MapperOfApprovalFlowReuseResponse.convertAmount(new BigDecimal("123.45"), null)

        then:
        result == null
    }

    def "convertCurrency - amount为null返回null"() {
        when:
        def result = MapperOfApprovalFlowReuseResponse.convertCurrency(null, "CNY")

        then:
        result == null
    }

    def "convertCurrency - currency为null返回null"() {
        when:
        def result = MapperOfApprovalFlowReuseResponse.convertCurrency(new BigDecimal("123.45"), null)

        then:
        result == null
    }

    def "addReuseResultInfoOfHotelName - newBook为null和空字符串直接return"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def hotelNameObj = Stub(MultipleLanguageText) {
            getTextGB() >> null
            getTextEn() >> null
        }
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getHotelName() >> hotelNameObj
        }
        def hotelInfo = Stub(HotelInfoType) {
            getHotelName() >> "复用单酒店"
            getHotelNameEn() >> "复用单酒店EN"
        }
        def orderDetail = Stub(OrderDetailInfoType) {
            getHotelInfo() >> hotelInfo
        }
        def list = []

        when:
        mapper.addReuseResultInfoOfHotelName(orderDetail, list, checkAvailInfo, "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfHotelName - reuseOrder为null和空字符串直接return"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def hotelNameObj = Stub(MultipleLanguageText) {
            getTextGB() >> "新单酒店"
            getTextEn() >> null
        }
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getHotelName() >> hotelNameObj
        }
        def orderDetail = Stub(OrderDetailInfoType) {
            getHotelInfo() >> null
        }
        def list = []

        when:
        mapper.addReuseResultInfoOfHotelName(orderDetail, list, checkAvailInfo, "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfHotelName - reuseOrder的getHotelName和getHotelNameEn都为null直接return"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def hotelNameObj = Stub(MultipleLanguageText) {
            getTextGB() >> "新单酒店"
            getTextEn() >> null
        }
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getHotelName() >> hotelNameObj
        }
        def hotelInfo = Stub(HotelInfoType) {
            getHotelName() >> null
            getHotelNameEn() >> null
        }
        def orderDetail = Stub(OrderDetailInfoType) {
            getHotelInfo() >> hotelInfo
        }
        def list = []

        when:
        mapper.addReuseResultInfoOfHotelName(orderDetail, list, checkAvailInfo, "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfStar - hotelInfo为null，starOfReuseOrder为0"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getStar() >> 5
        }
        def orderDetail = Stub(OrderDetailInfoType) {
            getHotelInfo() >> null
        }
        def list = []
        mapper.buildCheckResultTitleSharkKey(_) >> "sharkKey"

        when:
        mapper.addReuseResultInfoOfStar(orderDetail, list, checkAvailInfo, "TYPE")

        then:
        list.size() == 1
        list[0] instanceof ReuseResultInfo
        list[0].orderValue == "0"
        list[0].newBookValue == "5"
        list[0].checkResultTitle == "sharkKey"
        list[0].checkResultType == "TYPE"
    }

    def "addReuseResultInfoOfStar - hotelInfo不为null，starOfReuseOrder为hotelInfo.getStar()"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def checkAvailInfo = Stub(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getStar() >> 4
        }
        def hotelInfo = Stub(HotelInfoType) {
            getStar() >> 3
        }
        def orderDetail = Stub(OrderDetailInfoType) {
            getHotelInfo() >> hotelInfo
        }
        def list = []
        mapper.buildCheckResultTitleSharkKey(_) >> "sharkKey"

        when:
        mapper.addReuseResultInfoOfStar(orderDetail, list, checkAvailInfo, "TYPE")

        then:
        list.size() == 1
        list[0] instanceof ReuseResultInfo
        list[0].orderValue == "3"
        list[0].newBookValue == "4"
        list[0].checkResultType == "TYPE"
        list[0].checkResultTitle == "sharkKey"
    }

    def "addReuseResultInfoOfPolicyName - policyGetCorpUserInfoResponseType为null直接return"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def list = []

        when:
        mapper.addReuseResultInfoOfPolicyName(Stub(OrderCreateRequestType), list, null, Stub(GetCorpUserInfoResponseType), "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfPolicyName - policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse为null直接return"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def list = []

        when:
        mapper.addReuseResultInfoOfPolicyName(Stub(OrderCreateRequestType), list, Stub(GetCorpUserInfoResponseType), null, "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfPolicyName - policyNameOfNewBook为null或空直接return"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        mapper.buildPolicyName(_, _) >> null
        def list = []

        when:
        mapper.addReuseResultInfoOfPolicyName(Stub(OrderCreateRequestType), list, Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType), "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfPolicyName - policyNameOfReuseOrder为null或空直接return"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        mapper.buildPolicyName(_, _) >>> ["新单策略", null] // 第一次返回"新单策略"，第二次返回null
        def list = []

        when:
        mapper.addReuseResultInfoOfPolicyName(Stub(OrderCreateRequestType), list, Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType), "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfPolicyName - 正常分支"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        mapper.buildPolicyName(_, _) >>> ["新单策略", "复用单策略"]
        mapper.buildCheckResultTitleSharkKey(_) >> "sharkKey"
        def list = []

        when:
        mapper.addReuseResultInfoOfPolicyName(Stub(OrderCreateRequestType), list, Stub(GetCorpUserInfoResponseType), Stub(GetCorpUserInfoResponseType), "TYPE")

        then:
        list.size() == 1
        list[0] instanceof ReuseResultInfo
        list[0].orderValue == "复用单策略"
        list[0].newBookValue == "新单策略"
        list[0].checkResultType == "TYPE"
        list[0].checkResultTitle == "sharkKey"
    }

    def "buildPolicyName - 语言为中文且name有值"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def integrationSoaRequestType = Stub(IntegrationSoaRequestType) {
            getLanguage() >> LanguageLocaleEnum.ZH_CN.getLocaleString()
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getIntegrationSoaRequestType() >> integrationSoaRequestType
        }
        def policy = Stub(GetCorpUserInfoResponseType) {
            getName() >> "中文策略"
        }

        when:
        def result = mapper.buildPolicyName(orderCreateRequestType, policy)

        then:
        result == "中文策略"
    }

    def "buildPolicyName - 语言为中文但name无值，英文名全有值"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def integrationSoaRequestType = Stub(IntegrationSoaRequestType) {
            getLanguage() >> LanguageLocaleEnum.ZH_CN.getLocaleString()
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getIntegrationSoaRequestType() >> integrationSoaRequestType
        }
        def policy = Stub(GetCorpUserInfoResponseType) {
            getName() >> ""
            getNameENFirstName() >> "ENFirst"
            getNameENLastName() >> "ENLast"
        }

        when:
        def result = mapper.buildPolicyName(orderCreateRequestType, policy)

        then:
        result == "ENFirst/ENLast"
    }

    def "buildPolicyName - 语言为中文但name无值，英文名部分无值"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def integrationSoaRequestType = Stub(IntegrationSoaRequestType) {
            getLanguage() >> LanguageLocaleEnum.ZH_CN.getLocaleString()
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getIntegrationSoaRequestType() >> integrationSoaRequestType
        }
        def policy = Stub(GetCorpUserInfoResponseType) {
            getName() >> ""
            getNameENFirstName() >> "ENFirst"
            getNameENLastName() >> ""
        }

        when:
        def result = mapper.buildPolicyName(orderCreateRequestType, policy)

        then:
        result == null
    }

    def "buildPolicyName - 语言为英文，英文名全有值"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def integrationSoaRequestType = Stub(IntegrationSoaRequestType) {
            getLanguage() >> "en_US"
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getIntegrationSoaRequestType() >> integrationSoaRequestType
        }
        def policy = Stub(GetCorpUserInfoResponseType) {
            getName() >> ""
            getNameENFirstName() >> "ENFirst"
            getNameENLastName() >> "ENLast"
        }

        when:
        def result = mapper.buildPolicyName(orderCreateRequestType, policy)

        then:
        result == "ENFirst/ENLast"
    }

    def "buildPolicyName - 语言为英文，英文名部分无值"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def integrationSoaRequestType = Stub(IntegrationSoaRequestType) {
            getLanguage() >> "en_US"
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getIntegrationSoaRequestType() >> integrationSoaRequestType
        }
        def policy = Stub(GetCorpUserInfoResponseType) {
            getName() >> ""
            getNameENFirstName() >> ""
            getNameENLastName() >> "ENLast"
        }

        when:
        def result = mapper.buildPolicyName(orderCreateRequestType, policy)

        then:
        result == null
    }

    def "buildPolicyName - 语言为英文，英文名全无值"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def integrationSoaRequestType = Stub(IntegrationSoaRequestType) {
            getLanguage() >> "en_US"
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getIntegrationSoaRequestType() >> integrationSoaRequestType
        }
        def policy = Stub(GetCorpUserInfoResponseType) {
            getName() >> ""
            getNameENFirstName() >> ""
            getNameENLastName() >> ""
        }

        when:
        def result = mapper.buildPolicyName(orderCreateRequestType, policy)

        then:
        result == null
    }

    def "addReuseResultInfoOfRoomQuantity - orderDetailInfoTypeOfApprovalFlowReuse为null直接return"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def list = []

        when:
        mapper.addReuseResultInfoOfRoomQuantity(Stub(OrderCreateRequestType), list, null, "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfRoomQuantity - orderBasicInfo为null直接return"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def orderDetail = Stub(OrderDetailInfoType) {
            getOrderBasicInfo() >> null
        }
        def list = []

        when:
        mapper.addReuseResultInfoOfRoomQuantity(Stub(OrderCreateRequestType), list, orderDetail, "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfRoomQuantity - 正常分支"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def orderBasicInfo = Stub(OrderBasicInfoType) {
            getRoomQuantity() >> 2
        }
        def orderDetail = Stub(OrderDetailInfoType) {
            getOrderBasicInfo() >> orderBasicInfo
        }
        def hotelBookInput = Stub(HotelBookInput) {
            getRoomQuantity() >> 3
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getHotelBookInput() >> hotelBookInput
        }
        def list = []
        mapper.buildCheckResultTitleSharkKey(_) >> "sharkKey"
        new MockUp<BFFSharkUtil>() {
            @Mock
            static String getSharkValue(String key) { return key }
        }

        when:
        mapper.addReuseResultInfoOfRoomQuantity(orderCreateRequestType, list, orderDetail, "TYPE")

        then:
        list.size() == 1
        list[0] instanceof ReuseResultInfo
        list[0].orderValue == "2"
        list[0].newBookValue == "3"
        list[0].checkResultType == "TYPE"
        list[0].checkResultTitle == "sharkKey"
    }

    def "addReuseResultInfoOfPassenger - orderDetailInfoTypeOfApprovalFlowReuse为null直接return"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def list = []

        when:
        mapper.addReuseResultInfoOfPassenger(Stub(OrderCreateRequestType), list, null, Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig), "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfPassenger - orderClientList为空直接return"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def orderDetail = Stub(OrderDetailInfoType) {
            getOrderClientList() >> []
        }
        def list = []

        when:
        mapper.addReuseResultInfoOfPassenger(Stub(OrderCreateRequestType), list, orderDetail, Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig), "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfPassenger - orderClientList元素为null或clientName为空跳过"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def client1 = Stub(OrderClientInfoType) {
            getClientName() >> ""
        }
        def orderDetail = Stub(OrderDetailInfoType) {
            getOrderClientList() >> [null, client1]
        }
        def list = []

        when:
        mapper.addReuseResultInfoOfPassenger(Stub(OrderCreateRequestType), list, orderDetail, Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig), "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfPassenger - hotelBookPassengerInputs为空直接return"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def client = Stub(OrderClientInfoType) {
            getClientName() >> "张三"
        }
        def orderDetail = Stub(OrderDetailInfoType) {
            getOrderClientList() >> [client]
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getHotelBookPassengerInputs() >> []
        }
        def list = []

        when:
        mapper.addReuseResultInfoOfPassenger(orderCreateRequestType, list, orderDetail, Stub(WrapperOfCheckAvail.BaseCheckAvailInfo), Stub(QconfigOfCertificateInitConfig), "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfCheckIn - checkinOfNewBook为null直接return"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        // Mock L10n.dateTimeFormatter().ymdShortString() 返回null
        GroovyMock(L10n, global: true)
        L10n.dateTimeFormatter(_) >> new L10n.DateTimeFormatter("zh-CN")
        new MockUp<L10n.DateTimeFormatter>() {
            @Mock
            String ymdShortString(Date date) {
                return "2025/07/12"
            }
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getIntegrationSoaRequestType() >> Stub(IntegrationSoaRequestType) {
                getLanguage() >> "zh-CN"
            }
            getHotelBookInput() >> Stub(HotelBookInput) {
                getHotelDateRangeInfo() >> Stub(HotelDateRangeInfo) {
                    getCheckIn() >> "2025-07-12"
                }
            }
        }
        def orderDetail = Stub(OrderDetailInfoType)
        def list = []

        when:
        mapper.addReuseResultInfoOfCheckIn(orderCreateRequestType, list, orderDetail, "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfCheckIn - checkinOfReuseOrder为null直接return"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        GroovyMock(L10n, global: true)
        L10n.dateTimeFormatter(_) >> new L10n.DateTimeFormatter("zh-CN")
        new MockUp<L10n.DateTimeFormatter>() {
            @Mock
            String ymdShortString(Date date) {
                return "2025/07/12"
            }
        }
        mapper.buildCheckIn(_, _) >> null
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getIntegrationSoaRequestType() >> Stub(IntegrationSoaRequestType) {
                getLanguage() >> "zh-CN"
            }
            getHotelBookInput() >> Stub(HotelBookInput) {
                getHotelDateRangeInfo() >> Stub(HotelDateRangeInfo) {
                    getCheckIn() >> "2025-07-12"
                }
            }
        }
        def orderDetail = Stub(OrderDetailInfoType)
        def list = []

        when:
        mapper.addReuseResultInfoOfCheckIn(orderCreateRequestType, list, orderDetail, "TYPE")

        then:
        list.isEmpty()
    }

    def "addReuseResultInfoOfCheckIn - 正常分支"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        GroovyMock(L10n, global: true)
        L10n.dateTimeFormatter(_) >> new L10n.DateTimeFormatter("zh-CN")
        new MockUp<L10n.DateTimeFormatter>() {
            @Mock
            String ymdShortString(Date date) {
                return "2025/07/12"
            }
        }
        mapper.buildCheckIn(_, _) >> "2025/07/10"
        mapper.buildCheckResultTitleSharkKey(_) >> "sharkKey"
        new MockUp<BFFSharkUtil>() {
            @Mock
            static String getSharkValue(String key) { return key }
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getIntegrationSoaRequestType() >> Stub(IntegrationSoaRequestType) {
                getLanguage() >> "zh-CN"
            }
            getHotelBookInput() >> Stub(HotelBookInput) {
                getHotelDateRangeInfo() >> Stub(HotelDateRangeInfo) {
                    getCheckIn() >> "2025-07-12"
                }
            }
        }
        def orderDetail = Stub(OrderDetailInfoType)
        def list = []

        when:
        mapper.addReuseResultInfoOfCheckIn(orderCreateRequestType, list, orderDetail, "TYPE")

        then:
        list.size() == 1
        list[0] instanceof ReuseResultInfo
        list[0].orderValue == "2025/07/10"
        list[0].newBookValue == "2025/07/12"
        list[0].checkResultType == "TYPE"
        list[0].checkResultTitle == "sharkKey"
    }

    def "addReuseResultInfoOfCity - cityBaseInfoEntity 或 cityBaseInfoEntityOfApprovalFlowReuse 为 null 时不添加"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def list = []

        when:
        mapper.addReuseResultInfoOfCity(cityBaseInfoEntityOfApprovalFlowReuse, cityBaseInfoEntity, list, "TYPE")

        then:
        list.isEmpty()

        where:
        cityBaseInfoEntityOfApprovalFlowReuse | cityBaseInfoEntity
        null                                 | Stub(CityBaseInfoEntity)
        Stub(CityBaseInfoEntity)             | null
    }

    def "addReuseResultInfoOfCity - cityName 为空或null时不添加"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def city1 = Stub(CityBaseInfoEntity) {
            getCityName() >> cityName1
        }
        def city2 = Stub(CityBaseInfoEntity) {
            getCityName() >> cityName2
        }
        def list = []

        when:
        mapper.addReuseResultInfoOfCity(city1, city2, list, "TYPE")

        then:
        list.isEmpty()

        where:
        cityName1 | cityName2
        ""        | "北京"
        "上海"    | ""
        null      | "北京"
        "上海"    | null
    }

    def "addReuseResultInfoOfCity - 正常添加ReuseResultInfo"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def city1 = Stub(CityBaseInfoEntity) {
            getCityName() >> "上海"
        }
        def city2 = Stub(CityBaseInfoEntity) {
            getCityName() >> "北京"
        }
        def list = []
        mapper.buildCheckResultTitleSharkKey(_) >> "sharkKey"
        GroovyMock(BFFSharkUtil, global: true)
        BFFSharkUtil.getSharkValue(_) >> "sharkKey"

        when:
        mapper.addReuseResultInfoOfCity(city1, city2, list, "TYPE")

        then:
        list.size() == 1
        list[0].orderValue == "上海"
        list[0].newBookValue == "北京"
        list[0].checkResultType == "TYPE"
        list[0].checkResultTitle == "sharkKey"
    }

    def "buildCityBaseInfoEntityOfNewBook - getCityBaseInfoResponseType为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.buildCityBaseInfoEntityOfNewBook(Stub(OrderCreateRequestType), null)

        then:
        result == null
    }

    def "buildCityBaseInfoEntityOfNewBook - getCityBaseInfo为空或null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def getCityBaseInfoResponseType = Stub(GetCityBaseInfoResponseType) {
            getCityBaseInfo() >> cityBaseInfoList
        }

        when:
        def result = mapper.buildCityBaseInfoEntityOfNewBook(Stub(OrderCreateRequestType), getCityBaseInfoResponseType)

        then:
        result == null

        where:
        cityBaseInfoList << [null, []]
    }

    def "buildCityBaseInfoEntityOfNewBook - cityId为null或0返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def cityInput = Stub(CityInput) {
            getCityId() >> cityId
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getCityInput() >> cityInput
        }
        def getCityBaseInfoResponseType = Stub(GetCityBaseInfoResponseType) {
            getCityBaseInfo() >> [Stub(CityBaseInfoEntity)]
        }
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isZeroOrNull(cityId) >> true

        when:
        def result = mapper.buildCityBaseInfoEntityOfNewBook(orderCreateRequestType, getCityBaseInfoResponseType)

        then:
        result == null

        where:
        cityId << [null, 0L]
    }

    def "buildCityBaseInfoEntityOfNewBook - 没有匹配cityId返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def cityInput = Stub(CityInput) {
            getCityId() >> 123L
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getCityInput() >> cityInput
        }
        def city1 = Stub(CityBaseInfoEntity) {
            getCityId() >> 456L
        }
        def getCityBaseInfoResponseType = Stub(GetCityBaseInfoResponseType) {
            getCityBaseInfo() >> [city1]
        }
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isZeroOrNull(123L) >> false
        TemplateNumberUtil.isNotZeroAndNull(456L) >> true

        when:
        def result = mapper.buildCityBaseInfoEntityOfNewBook(orderCreateRequestType, getCityBaseInfoResponseType)

        then:
        result == null
    }

    def "buildCityBaseInfoEntityOfNewBook - 匹配cityId返回对应CityBaseInfoEntity"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def cityInput = Stub(CityInput) {
            getCityId() >> 123L
        }
        def orderCreateRequestType = Stub(OrderCreateRequestType) {
            getCityInput() >> cityInput
        }
        def city1 = Stub(CityBaseInfoEntity) {
            getCityId() >> 123L
        }
        def getCityBaseInfoResponseType = Stub(GetCityBaseInfoResponseType) {
            getCityBaseInfo() >> [city1]
        }
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isZeroOrNull(123L) >> false
        TemplateNumberUtil.isNotZeroAndNull(123L) >> true

        when:
        def result = mapper.buildCityBaseInfoEntityOfNewBook(orderCreateRequestType, getCityBaseInfoResponseType)

        then:
        result == city1
    }

    def "buildCityBaseInfoEntityOfApprovalFlowReuse - getCityBaseInfoResponseTypeOfApprovalFlowReuse为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.buildCityBaseInfoEntityOfApprovalFlowReuse(null, Stub(OrderDetailInfoType))

        then:
        result == null
    }

    def "buildCityBaseInfoEntityOfApprovalFlowReuse - getCityBaseInfo为空或null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def getCityBaseInfoResponseType = Stub(GetCityBaseInfoResponseType) {
            getCityBaseInfo() >> cityBaseInfoList
        }

        when:
        def result = mapper.buildCityBaseInfoEntityOfApprovalFlowReuse(getCityBaseInfoResponseType, Stub(OrderDetailInfoType))

        then:
        result == null

        where:
        cityBaseInfoList << [null, []]
    }

    def "buildCityBaseInfoEntityOfApprovalFlowReuse - cityId为null或0返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def hotelAreaInfo = Stub(HotelAreaInfoType) {
            getCityId() >> cityId
        }
        def hotelInfo = Stub(HotelInfoType) {
            getHotelAreaInfo() >> hotelAreaInfo
        }
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getHotelInfo() >> hotelInfo
        }
        def getCityBaseInfoResponseType = Stub(GetCityBaseInfoResponseType) {
            getCityBaseInfo() >> [Stub(CityBaseInfoEntity)]
        }
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isZeroOrNull(cityId) >> true

        when:
        def result = mapper.buildCityBaseInfoEntityOfApprovalFlowReuse(getCityBaseInfoResponseType, orderDetailInfoType)

        then:
        result == null

        where:
        cityId << [null, 0L]
    }

    def "buildCityBaseInfoEntityOfApprovalFlowReuse - 没有匹配cityId返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def hotelAreaInfo = Stub(HotelAreaInfoType) {
            getCityId() >> 123L
        }
        def hotelInfo = Stub(HotelInfoType) {
            getHotelAreaInfo() >> hotelAreaInfo
        }
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getHotelInfo() >> hotelInfo
        }
        def city1 = Stub(CityBaseInfoEntity) {
            getCityId() >> 456L
        }
        def getCityBaseInfoResponseType = Stub(GetCityBaseInfoResponseType) {
            getCityBaseInfo() >> [city1]
        }
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isZeroOrNull(123L) >> false
        TemplateNumberUtil.isNotZeroAndNull(456L) >> true

        when:
        def result = mapper.buildCityBaseInfoEntityOfApprovalFlowReuse(getCityBaseInfoResponseType, orderDetailInfoType)

        then:
        result == null
    }

    def "buildCityBaseInfoEntityOfApprovalFlowReuse - 匹配cityId返回对应CityBaseInfoEntity"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def hotelAreaInfo = Stub(HotelAreaInfoType) {
            getCityId() >> 123L
        }
        def hotelInfo = Stub(HotelInfoType) {
            getHotelAreaInfo() >> hotelAreaInfo
        }
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getHotelInfo() >> hotelInfo
        }
        def city1 = Stub(CityBaseInfoEntity) {
            getCityId() >> 123L
        }
        def getCityBaseInfoResponseType = Stub(GetCityBaseInfoResponseType) {
            getCityBaseInfo() >> [city1]
        }
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isZeroOrNull(123L) >> false
        TemplateNumberUtil.isNotZeroAndNull(123L) >> true

        when:
        def result = mapper.buildCityBaseInfoEntityOfApprovalFlowReuse(getCityBaseInfoResponseType, orderDetailInfoType)

        then:
        result == city1
    }


    def "buildResourceNameContent - orderDetailInfoType为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.buildResourceNameContent(null, Stub(IntegrationSoaRequestType))

        then:
        result == null
    }

    def "buildResourceNameContent - orderDetailInfoType.getHotelInfo为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getHotelInfo() >> null
        }

        when:
        def result = mapper.buildResourceNameContent(orderDetailInfoType, Stub(IntegrationSoaRequestType))

        then:
        result == null
    }

    def "buildResourceNameContent - 正常流程返回ResourceNameContent并设置字段"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def hotelInfo = Stub(HotelInfoType) {
            getHotelName() >> "测试酒店"
        }
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getHotelInfo() >> hotelInfo
        }
        def integrationSoaRequestType = Stub(IntegrationSoaRequestType) {
            getLanguage() >> "zh_CN"
        }

        when:
        def result = mapper.buildResourceNameContent(orderDetailInfoType, integrationSoaRequestType)

        then:
        result != null
        result instanceof ResourceNameContent
        result.getLocale() == "zh_CN"
        result.getName() == "测试酒店"
    }

    def "buildCheckOut - orderDetailInfoType为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.buildCheckOut(Stub(IntegrationSoaRequestType), null)

        then:
        result == null
    }

    def "buildCheckOut - orderDetailInfoType.getRoomInfo为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getRoomInfo() >> null
        }

        when:
        def result = mapper.buildCheckOut(Stub(IntegrationSoaRequestType), orderDetailInfoType)

        then:
        result == null
    }

    def "buildCheckOut - departureDay为空或null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def roomInfo = Stub(RoomInfoType) {
            getDepartureDay() >> departureDay
        }
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getRoomInfo() >> roomInfo
        }
        GroovyMock(StringUtil, global: true)
        StringUtil.isBlank(departureDay) >> true

        when:
        def result = mapper.buildCheckOut(Stub(IntegrationSoaRequestType), orderDetailInfoType)

        then:
        result == null

        where:
        departureDay << [null, ""]
    }

    def "buildCheckOut - 正常流程返回格式化日期字符串"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def roomInfo = Stub(RoomInfoType) {
            getDepartureDay() >> "2025-08-01 12:00:00"
        }
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getRoomInfo() >> roomInfo
        }
        def integrationSoaRequestType = Stub(IntegrationSoaRequestType) {
            getLanguage() >> "zh_CN"
        }
        GroovyMock(StringUtil, global: true)
        StringUtil.isBlank("2025-08-01 12:00:00") >> false
        GroovyMock(DateUtil, global: true)
        def mockDate = new Date()
        DateUtil.parseDate("2025-08-01 12:00:00", DateUtil.YYYY_MM_DD_HH_mm_ss) >> mockDate

        // 用JMockit mock final类
        new MockUp<L10n.DateTimeFormatter>() {
            @Mock
            String ymdShortString(Date d) {
                return "2025/08/01"
            }
        }
        new MockUp<L10n>() {
            @Mock
            L10n.DateTimeFormatter dateTimeFormatter(String lang) {
                return new L10n.DateTimeFormatter(lang)
            }
        }

        when:
        def result = mapper.buildCheckOut(integrationSoaRequestType, orderDetailInfoType)

        then:
        result == "2025/08/01"
    }

    def "buildCheckIn - orderDetailInfoType为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.buildCheckIn(Stub(IntegrationSoaRequestType), null)

        then:
        result == null
    }

    def "buildCheckIn - orderDetailInfoType.getRoomInfo为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getRoomInfo() >> null
        }

        when:
        def result = mapper.buildCheckIn(Stub(IntegrationSoaRequestType), orderDetailInfoType)

        then:
        result == null
    }

    def "buildCheckIn - arrivalDay为空或null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def roomInfo = Stub(RoomInfoType) {
            getArrivalDay() >> arrivalDay
        }
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getRoomInfo() >> roomInfo
        }
        GroovyMock(StringUtil, global: true)
        StringUtil.isBlank(arrivalDay) >> true

        when:
        def result = mapper.buildCheckIn(Stub(IntegrationSoaRequestType), orderDetailInfoType)

        then:
        result == null

        where:
        arrivalDay << [null, ""]
    }

    def "buildCheckIn - 正常流程返回格式化日期字符串"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def roomInfo = Stub(RoomInfoType) {
            getArrivalDay() >> "2025-08-01 12:00:00"
        }
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getRoomInfo() >> roomInfo
        }
        def integrationSoaRequestType = Stub(IntegrationSoaRequestType) {
            getLanguage() >> "zh_CN"
        }
        GroovyMock(StringUtil, global: true)
        StringUtil.isBlank("2025-08-01 12:00:00") >> false
        GroovyMock(DateUtil, global: true)
        def mockDate = new Date()
        DateUtil.parseDate("2025-08-01 12:00:00", DateUtil.YYYY_MM_DD_HH_mm_ss) >> mockDate

        // 用JMockit mock final类
        new MockUp<L10n.DateTimeFormatter>() {
            @Mock
            String ymdShortString(Date d) {
                return "2025/08/01"
            }
        }
        new MockUp<L10n>() {
            @Mock
            L10n.DateTimeFormatter dateTimeFormatter(String lang) {
                return new L10n.DateTimeFormatter(lang)
            }
        }

        when:
        def result = mapper.buildCheckIn(integrationSoaRequestType, orderDetailInfoType)

        then:
        result == "2025/08/01"
    }

    def "buildReuseOrderInfo - orderDetailInfoTypeOfApprovalFlowReuse为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.buildReuseOrderInfo(null)

        then:
        result == null
    }

    def "buildReuseOrderInfo - orderId为null或0返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getOrderId() >> orderId
        }
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isZeroOrNull(orderId) >> true

        when:
        def result = mapper.buildReuseOrderInfo(orderDetailInfoType)

        then:
        result == null

        where:
        orderId << [null, 0L]
    }

    def "buildReuseOrderInfo - paymentInfo为null只设置orderId"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getOrderId() >> 123L
            getPaymentInfo() >> null
        }
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isZeroOrNull(123L) >> false

        when:
        def result = mapper.buildReuseOrderInfo(orderDetailInfoType)

        then:
        result != null
        result instanceof ReuseOrderInfo
        result.getOrderId() == "123"
        result.getAmountInfo() == null
    }

    def "buildReuseOrderInfo - paymentInfo不满足条件只设置orderId"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def paymentInfo = Stub(PaymentInfoType) {
            getSettlementCurrency() >> ""
            getSettlementOrderAmount() >> new BigDecimal("0")
        }
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getOrderId() >> 123L
            getPaymentInfo() >> paymentInfo
        }
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isZeroOrNull(123L) >> false
        GroovyMock(StringUtil, global: true)
        StringUtil.isNotBlank("") >> false
        GroovyMock(MathUtils, global: true)
        MathUtils.isGreaterThanZero(new BigDecimal("0")) >> false

        when:
        def result = mapper.buildReuseOrderInfo(orderDetailInfoType)

        then:
        result != null
        result instanceof ReuseOrderInfo
        result.getOrderId() == "123"
        result.getAmountInfo() == null
    }

    def "buildReuseOrderInfo - paymentInfo满足条件设置orderId和amountInfo"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def paymentInfo = Stub(PaymentInfoType) {
            getSettlementCurrency() >> "CNY"
            getSettlementOrderAmount() >> new BigDecimal("100")
        }
        def orderDetailInfoType = Stub(OrderDetailInfoType) {
            getOrderId() >> 123L
            getPaymentInfo() >> paymentInfo
        }
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isZeroOrNull(123L) >> false
        GroovyMock(StringUtil, global: true)
        StringUtil.isNotBlank("CNY") >> true
        GroovyMock(MathUtils, global: true)
        MathUtils.isGreaterThanZero(new BigDecimal("100")) >> true

        new MockUp<MapperOfApprovalFlowReuseResponse>() {
            @Mock
            static String convertCurrency(BigDecimal amount, String currency) { return "CNY" }
            @Mock
            static String convertAmount(BigDecimal amount, String currency) { return "100.00" }
        }

        when:
        def result = mapper.buildReuseOrderInfo(orderDetailInfoType)

        then:
        result != null
        result instanceof ReuseOrderInfo
        result.getOrderId() == "123"
        result.getAmountInfo() != null
        result.getAmountInfo().getCurrency() == "CNY"
        result.getAmountInfo().getAmount() == "100.00"
    }

    def "builTripInfo - searchTripBasicInfoResponseTypeOfApprovalFlowReuse为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()

        when:
        def result = mapper.builTripInfo(null)

        then:
        result == null
    }

    def "builTripInfo - getBasicInfo为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def searchTripBasicInfoResponseType = Stub(SearchTripBasicInfoResponseType) {
            getBasicInfo() >> null
        }

        when:
        def result = mapper.builTripInfo(searchTripBasicInfoResponseType)

        then:
        result == null
    }

    def "builTripInfo - tripName为空或null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def basicInfo = Stub(BasicInfo) {
            getTripName() >> tripName
            getTripId() >> 123L
        }
        def searchTripBasicInfoResponseType = Stub(SearchTripBasicInfoResponseType) {
            getBasicInfo() >> basicInfo
        }
        GroovyMock(StringUtil, global: true)
        StringUtil.isBlank(tripName) >> true

        when:
        def result = mapper.builTripInfo(searchTripBasicInfoResponseType)

        then:
        result == null

        where:
        tripName << [null, ""]
    }

    def "builTripInfo - tripId为null或0返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def basicInfo = Stub(BasicInfo) {
            getTripName() >> "出差单"
            getTripId() >> tripId
        }
        def searchTripBasicInfoResponseType = Stub(SearchTripBasicInfoResponseType) {
            getBasicInfo() >> basicInfo
        }
        GroovyMock(StringUtil, global: true)
        StringUtil.isBlank("出差单") >> false
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isZeroOrNull(tripId) >> true

        when:
        def result = mapper.builTripInfo(searchTripBasicInfoResponseType)

        then:
        result == null

        where:
        tripId << [null, 0L]
    }

    def "builTripInfo - 正常流程返回TripInfo并设置字段"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        def basicInfo = Stub(BasicInfo) {
            getTripName() >> "出差单"
            getTripId() >> 123L
        }
        def searchTripBasicInfoResponseType = Stub(SearchTripBasicInfoResponseType) {
            getBasicInfo() >> basicInfo
        }
        GroovyMock(StringUtil, global: true)
        StringUtil.isBlank("出差单") >> false
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isZeroOrNull(123L) >> false

        when:
        def result = mapper.builTripInfo(searchTripBasicInfoResponseType)

        then:
        result != null
        result instanceof TripInfo
        result.getTripId() == "123"
        result.getTripName() == "出差单"
    }

    def "getOrderCreateTokenReuseByCheck - 正常流程应调用addContinueTypes和setFollowApprovalResult并返回token"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        def checkHotelAuthExtensionResponseType = Stub(CheckHotelAuthExtensionResponseType)
        def orderCreateRequestType = Stub(OrderCreateRequestType)
        def getOrderFoundationDataResponseType = Stub(GetOrderFoundationDataResponseType)
        def strategyInfoMap = Stub(Map)
        def orderCreateToken = Mock(OrderCreateToken)

        def mockFollowApprovalResult = Stub(FollowApprovalResult)
        mapper.buildCanFollowApprovalResultByCheck(
                checkHotelAuthExtensionResponseType,
                orderCreateRequestType,
                getOrderFoundationDataResponseType,
                strategyInfoMap
        ) >> mockFollowApprovalResult

        when:
        def result = mapper.getOrderCreateTokenReuseByCheck(
                checkHotelAuthExtensionResponseType,
                orderCreateRequestType,
                getOrderFoundationDataResponseType,
                strategyInfoMap,
                orderCreateToken
        )

        then:
        1 * orderCreateToken.addContinueTypes(ContinueTypeConst.APPROVAL_FLOW_REUSE)
        1 * orderCreateToken.setFollowApprovalResult(mockFollowApprovalResult)
        result == orderCreateToken
    }

    def "buildCanFollowApprovalResultByCheck - checkHotelAuthExtensionResponseType为null返回null"() {
        given:
        def mapper = new MapperOfApprovalFlowReuseResponse()
        GroovyMock(BooleanUtil, global: true)
        BooleanUtil.parseStr(false) >> false
        GroovyMock(TemplateNumberUtil, global: true)
        GroovyMock(OrderCreateProcessorOfUtil, global: true)
        TemplateNumberUtil.isNotZeroAndNull(_) >> false

        when:
        def result = mapper.buildCanFollowApprovalResultByCheck(
                null,
                Stub(OrderCreateRequestType),
                Stub(GetOrderFoundationDataResponseType),
                new HashMap<String, StrategyInfo>()
        )

        then:
        result == null
    }

    def "buildReuseResultInfos - reasonList为空或null返回null"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)

        when:
        def result1 = mapper.buildReuseResultInfos(null, null, null, null, null, null, null, null, null)
        def result2 = mapper.buildReuseResultInfos([], null, null, null, null, null, null, null, null)

        then:
        result1 == null
        result2 == null
    }

    def "buildReuseResultInfos - reason为空字符串或不在COMPARE_WITH_REUSE_ORDER跳过"() {
        given:
        def mapper = Spy(MapperOfApprovalFlowReuseResponse)
        mapper.metaClass.static.getProperty = { String name ->
            if (name == "COMPARE_WITH_REUSE_ORDER") return ["CITY_ID_DIFFERENT"]
            return MapperOfApprovalFlowReuseResponse.metaClass.getProperty(name)
        }

        when:
        def result = mapper.buildReuseResultInfos(["", "NOT_IN"], null, null, null, Stub(OrderDetailInfoType), null, null, null, null)

        then:
        result.isEmpty()
    }

    def "buildSupportReloadTripModule - CorpPayInfoUtil.isPrivate为true时返回false"() {
        given:
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def corpPayInfo = new CorpPayInfo("private")
        orderCreateRequestType.getCorpPayInfo() >> corpPayInfo

        GroovyMock(CorpPayInfoUtil, global: true)
        CorpPayInfoUtil.isPrivate(corpPayInfo) >> true
        CorpPayInfoUtil.check(corpPayInfo) >> true

        when:
        def result = new MapperOfApprovalFlowReuseResponse().buildSupportReloadTripModule(accountInfo, orderCreateRequestType)

        then:
        result == BooleanUtil.parseStr(false)
    }

    def "buildSupportReloadTripModule - isPrivate为false, isPackageEnabled为false时返回false"() {
        given:
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def corpPayInfo = new CorpPayInfo("public")
        orderCreateRequestType.getCorpPayInfo() >> corpPayInfo

        GroovyMock(CorpPayInfoUtil, global: true)
        CorpPayInfoUtil.isPrivate(corpPayInfo) >> false
        CorpPayInfoUtil.check(corpPayInfo) >> true

        accountInfo.isPackageEnabled() >> false

        when:
        def result = new MapperOfApprovalFlowReuseResponse().buildSupportReloadTripModule(accountInfo, orderCreateRequestType)

        then:
        result == BooleanUtil.parseStr(false)
    }

    def "buildSupportReloadTripModule - isPrivate为false, isPackageEnabled为true时返回true"() {
        given:
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def corpPayInfo = new CorpPayInfo("public")
        orderCreateRequestType.getCorpPayInfo() >> corpPayInfo

        GroovyMock(CorpPayInfoUtil, global: true)
        CorpPayInfoUtil.isPrivate(corpPayInfo) >> false
        CorpPayInfoUtil.check(corpPayInfo) >> true

        accountInfo.isPackageEnabled() >> true

        when:
        def result = new MapperOfApprovalFlowReuseResponse().buildSupportReloadTripModule(accountInfo, orderCreateRequestType)

        then:
        result == BooleanUtil.parseStr(true)
    }

}