package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.soa._21234.GetUserLiteInfoResponseType
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;

class MapperOfCreateTripRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfCreateTripRequestType())
    def savePoint = new mockit.internal.state.SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        expect:
        tester.convert(Tuple3.of(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a"))), new GetUserLiteInfoResponseType(), [:])) .uid == "a"
    }
}
