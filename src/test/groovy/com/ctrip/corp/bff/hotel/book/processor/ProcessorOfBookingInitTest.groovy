package com.ctrip.corp.bff.hotel.book.processor

import com.ctrip.corp.bff.framework.hotel.common.enums.TrackingEnum
import com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalOutput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPositionInfo
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.AggCostAllocationToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.CouponToken
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import com.ctrip.corp.bff.hotel.book.contract.BookingHotelInfo
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.hotel.book.contract.BookingInitResponseType
import com.ctrip.corp.bff.hotel.book.contract.RoomProperty
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType
import mockit.MockUp
import mockit.internal.state.SavePoint
import org.mockito.Mock
import spock.lang.Specification

/**
 * <AUTHOR>
 * @Date 2024/12/3 21:00
 */
class ProcessorOfBookingInitTest extends Specification {

    def savePoint = new SavePoint()

    def setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }
    def processorOfBookingInit = new ProcessorOfBookingInit()

    def testBuildApprovalInput() {
        expect:
        processorOfBookingInit.buildApprovalInput(requestType, approvalOutput) == result
        where:
        requestType                                                                                                     | approvalOutput                                                                                   || result
        null                                                                                                            | null                                                                                             || null
        new BookingInitRequestType(strategyInfos: [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "T")]) | new com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalOutput(defaultApprovalSubNo: "1") || new ApprovalInput(subApprovalNo: "1")
        new BookingInitRequestType(strategyInfos: [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "F")]) | new com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalOutput(defaultApprovalSubNo: "1") || null
        new BookingInitRequestType(strategyInfos: [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "T")]) | null                                                                                             || null
    }


    def "testTracking"() {
        given:
        BookingInitRequestType requestType = new BookingInitRequestType()
        HotelBookInput hotelBookInput = new HotelBookInput()
        HotelDateRangeInfo hotelDateRangeInfo = new HotelDateRangeInfo()
        hotelDateRangeInfo.setCheckIn("2024-12-18")
        hotelDateRangeInfo.setCheckOut("2024-12-20")
        hotelBookInput.setHotelDateRangeInfo(hotelDateRangeInfo)
        requestType.setHotelBookInput(hotelBookInput)
        BookingInitResponseType responseType = new BookingInitResponseType(
                bookingHotelInfo: new BookingHotelInfo(
                        hotelId: "890890",
                        hotelPositionInfo: new HotelPositionInfo(cityInfo: new CityInfo(cityId: 1, cityArea: "HK"))),
                approvalOutput: new ApprovalOutput(defaultApprovalSubNo: "123", defaultEmergencyBook: "F"))

        when:
        Map<String, String> trackingMap = processorOfBookingInit.tracking(requestType, responseType)

        then:
        trackingMap != null
        trackingMap.get("cityRegion") == "HK"
        trackingMap.get("checkIn") == "2024-12-18"
        trackingMap.get("cityId") == "1"
        trackingMap.get("checkOut") == "2024-12-20"
        trackingMap.get("hotelId") == "890890"
        trackingMap.get(TrackingEnum.BOOK_MODE.getCode()) == "APPROVAL"
    }

    def "test trackingForRoomProperties when roomProperties is empty"() {
        given:
        List<RoomProperty> roomProperties = []

        when:
        Map<String, String> result = processorOfBookingInit.trackingForRoomProperties(roomProperties)

        then:
        result == null
    }

    def "test trackingForRoomProperties when roomProperties contains elements with same key"() {
        given:
        List<RoomProperty> roomProperties = [
                new RoomProperty(propertyType: "CERTIFICATE_ROOM", propertyValue: "T"),
                new RoomProperty(propertyType: "CERTIFICATE_ROOM", propertyValue: "F")
        ]

        when:
        Map<String, String> result = processorOfBookingInit.trackingForRoomProperties(roomProperties)

        then:
        result.size() == 1
        result["CERTIFICATE_ROOM"] == "T|F"
    }

    def "test trackingForRoomProperties when roomProperties contains elements with different keys"() {
        given:
        List<RoomProperty> roomProperties = [
                new RoomProperty(propertyType: "CERTIFICATE_ROOM", propertyValue: "T"),
                new RoomProperty(propertyType: "OTHER_ROOM", propertyValue: "F")
        ]

        when:
        Map<String, String> result = processorOfBookingInit.trackingForRoomProperties(roomProperties)

        then:
        result.size() == 2
        result["CERTIFICATE_ROOM"] == "T"
        result["OTHER_ROOM"] == "F"
    }

    def "test mapCertificateRoomProperties when roomProperties is empty"() {
        given:
        List<RoomProperty> roomProperties = []

        when:
        Map<String, List<RoomProperty>> result = processorOfBookingInit.mapCertificateRoomProperties(roomProperties)

        then:
        result == null
    }

    def "test roomPropertyToString when roomProperties is empty"() {
        given:
        List<RoomProperty> roomProperties = []

        when:
        String result = processorOfBookingInit.roomPropertyToString(roomProperties)

        then:
        result == ""
    }

    def "test"() {
        given:
        when:
        // 期望
        def a = TokenParseUtil.parseToken("H4sIAAAAAAAA_-PiMjK0NLY0MjAxN5OS4Wi4cOLAzScMAgwSIkp8xiZmegZwoIVX1givrBWjGwCzt7NzaQAAAA", BookInitToken .class)
        // 实际
        def b = TokenParseUtil.parseToken("H4sIAAAAAAAA_-PiMjK0NLY0MjAxN5OS4Wi4cOLAzScMAgwSIkp8xiZmegZwoIVX1givrBWjGwCzt7NzaQAAAA", BookInitToken.class)
        then:
        System.out.println(JsonUtil.toJson(a))
        System.out.println(JsonUtil.toJson(b))
        JsonUtil.toJson(a) == JsonUtil.toJson(b)
    }


    def "testCheckGetSupportedPaymentMethodResponseType with invalid response"() {
        given: "A ProcessorOfBookingInit instance and a null GetSupportedPaymentMethodResponseType"
        def processor = new ProcessorOfBookingInit()
        new MockUp<BFFSharkUtil>() {
            @mockit.Mock
            public static String getSharkValue(String key) {
                if (key.contains("665")) {
                    return null
                }
                return key + ":value"
            }
        }

        when: "Calling checkGetSupportedPaymentMethodResponseType"
        processor.checkGetSupportedPaymentMethodResponseType(null)

        then: "An exception is thrown"
        def exception = thrown(BusinessException)
        exception.errorCode == 665
        exception.friendlyMessage == "key.corp.hotel.CorpHotelBookCommonWS.getSupportedPaymentMethod.errorMsg:value"

        when: "Calling checkGetSupportedPaymentMethodResponseType"
        processor.checkGetSupportedPaymentMethodResponseType(new GetSupportedPaymentMethodResponseType(responseCode: null))

        then: "An exception is thrown"
        exception = thrown(BusinessException)
        exception.errorCode == 665
        exception.friendlyMessage == "key.corp.hotel.CorpHotelBookCommonWS.getSupportedPaymentMethod.errorMsg:value"


        when: "Calling checkGetSupportedPaymentMethodResponseType"
        processor.checkGetSupportedPaymentMethodResponseType(new GetSupportedPaymentMethodResponseType(responseCode: 6767))

        then: "An exception is thrown"
        exception = thrown(BusinessException)
        exception.errorCode == 665
        exception.friendlyMessage == "key.corp.hotel.CorpHotelBookCommonWS.getSupportedPaymentMethod.errorMsg.6767:value"

        when: "Calling checkGetSupportedPaymentMethodResponseType"
        processor.checkGetSupportedPaymentMethodResponseType(new GetSupportedPaymentMethodResponseType(responseCode: 20000))

        then: "no exception is thrown"
        noExceptionThrown()
    }
}
