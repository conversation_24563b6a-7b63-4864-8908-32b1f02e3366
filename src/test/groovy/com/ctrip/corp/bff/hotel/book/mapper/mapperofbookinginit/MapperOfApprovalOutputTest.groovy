package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalOutput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.utils.ResourceTokenUtil
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.specific.contract.ApprovalBaseInfo
import com.ctrip.corp.bff.specific.contract.ApprovalDefaultResponseType
import com.ctrip.corp.bff.specific.contract.ApprovalInfo
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultResponseType
import com.ctrip.corp.order.data.aggregation.query.contract.ApprovalType
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType
import com.ctrip.corp.order.data.aggregation.query.contract.TravelInfoType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR> @date 2024-07-16
 * */
class MapperOfApprovalOutputTest extends Specification {

    def mapperOfApprovalOutput = Spy(new MapperOfApprovalOutput())

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    def "getOrderId" () {
        given:
        new MockUp<ResourceTokenUtil>() {
            @Mock
            ResourceToken tryParseToken(String token) {
                ResourceToken resourceToken = new ResourceToken()
                OrderResourceToken orderResourceToken = new OrderResourceToken()
                orderResourceToken.setOrderId(123L)
                resourceToken.setOrderResourceToken(orderResourceToken)
                return resourceToken
            }
        }
        def BookingInitRequestType = new BookingInitRequestType()
        BookingInitRequestType.setOrderId("1234")
        def orderId = mapperOfApprovalOutput.getOrderId(BookingInitRequestType)
        expect:
        orderId == 123L
        new MockUp<ResourceTokenUtil>() {
            @Mock
            ResourceToken tryParseToken(String token) {
                ResourceToken resourceToken = new ResourceToken()
                OrderResourceToken orderResourceToken = new OrderResourceToken()
                orderResourceToken.setOrderId(null)
                resourceToken.setOrderResourceToken(orderResourceToken)
                return resourceToken
            }
        }
        def orderId2 = mapperOfApprovalOutput.getOrderId(BookingInitRequestType)
        orderId2 == 1234L
    }

    def "getApprovalOutputByDefault"() {
        given:
        BatchApprovalDefaultResponseType approvalDefaultResponseType = new BatchApprovalDefaultResponseType();
        when:
        def approvalOutput = mapperOfApprovalOutput.getApprovalOutputByDefault(null, null)
        then:
        approvalOutput == null
        when:
        ApprovalInfo approvalInfo = new ApprovalInfo()
        ApprovalBaseInfo approvalBaseInfo = new ApprovalBaseInfo()
        approvalBaseInfo.setSubApprovalNo("subNo")
        approvalBaseInfo.setMasterApprovalNo("mainNo")
        approvalInfo.setApprovalBaseInfo(approvalBaseInfo)
        approvalInfo.setDefaultApproval("T")
        approvalDefaultResponseType.setApprovalInfos(Arrays.asList(approvalInfo))
        def approvalOutput2 = mapperOfApprovalOutput.getApprovalOutputByDefault(new ApprovalOutput(), approvalDefaultResponseType)
        then:
        approvalOutput2.getDefaultApprovalMainNo() == "mainNo"
        approvalOutput2.getDefaultApprovalSubNo() == "subNo"
        approvalOutput2.getDefaultEmergencyBook() == "F"
    }

    def "orderApproval"() {
        given:
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType = new QueryHotelOrderDataResponseType()
        TravelInfoType travelInfoType = new TravelInfoType()
        ApprovalType approvalType = new ApprovalType()
        approvalType.setPrepareApprovalStatus(2)
        approvalType.setSubPreApprovalNo("subNo")
        approvalType.setPrepareApprovalNo("mainNo")
        travelInfoType.setApproval(approvalType)
        queryHotelOrderDataResponseType.setTravelInfo(travelInfoType)
        when:
        def approvalOutput = mapperOfApprovalOutput.orderApproval(new ApprovalOutput(), queryHotelOrderDataResponseType)
        then:
        approvalOutput.getDefaultEmergencyBook() == "F"
        approvalOutput.getDefaultApprovalSubNo() == "subNo"
        approvalOutput.getDefaultApprovalMainNo() == "mainNo"
    }

    def "preApproval"() {
        given:
        BatchApprovalDefaultResponseType approvalDefaultResponseType = new BatchApprovalDefaultResponseType();
        ApprovalInfo approvalInfo = new ApprovalInfo()
        ApprovalBaseInfo approvalBaseInfo = new ApprovalBaseInfo()
        approvalBaseInfo.setSubApprovalNo("subNo")
        approvalBaseInfo.setMasterApprovalNo("mainNo")
        approvalInfo.setApprovalBaseInfo(approvalBaseInfo)
        approvalInfo.setDefaultApproval("T")
        approvalDefaultResponseType.setApprovalInfos(Arrays.asList(approvalInfo))
        when:
        def approvalOutput = mapperOfApprovalOutput.preApproval(new ApprovalOutput(),"requestSubNo", "requestMainNo", approvalDefaultResponseType)
        then:
        approvalOutput.getDefaultApprovalMainNo() == "requestMainNo"
        approvalOutput.getDefaultApprovalSubNo() == "requestSubNo"
        approvalOutput.getDefaultEmergencyBook() == "F"
        when:
        def approvalOutput2 = mapperOfApprovalOutput.preApproval(new ApprovalOutput(),"", "", approvalDefaultResponseType)
        then:
        approvalOutput2.getDefaultApprovalMainNo() == "mainNo"
        approvalOutput2.getDefaultApprovalSubNo() == "subNo"
        approvalOutput2.getDefaultEmergencyBook() == "F"
    }
}
