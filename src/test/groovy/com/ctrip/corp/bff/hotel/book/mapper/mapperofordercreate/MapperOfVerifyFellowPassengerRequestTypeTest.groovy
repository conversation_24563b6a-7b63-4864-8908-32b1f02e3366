package com.ctrip.corp.bff.hotel.book.mapper.ordercreate

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.MapperOfVerifyFellowPassengerRequestType
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.corp.corpsz.preapprovalws.common.contract.BookingSourceEnum
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfVerifyFellowPassengerRequestTypeTest extends Specification {
    def myTestClass = new MapperOfVerifyFellowPassengerRequestType()

    def savePoint = new SavePoint()

    void setup() {
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "test getSourceFrom H5"() {
        given:

        when:
        def result = myTestClass.getSourceFrom(SourceFrom.H5)

        then:
        result == BookingSourceEnum.MOBILE
    }

    def "test getPolicyInfo"() {
        given:
        HotelPolicyInput hotelPolicyInput = new HotelPolicyInput()

        ApprovalInput approvalInput = new ApprovalInput();
        approvalInput.setMasterApprovalNo("114514")
        approvalInput.setSubApprovalNo("sub")
        hotelPolicyInput.setApprovalInput(approvalInput)

        PolicyInput policyInput = new PolicyInput()
        policyInput.setBookingType("booking type")
        policyInput.setPolicyUid("uid")
        hotelPolicyInput.setPolicyInput(policyInput)

        when:
        def result = myTestClass.getPolicyInfo(hotelPolicyInput)

        then:
        result.getApprovalNumber() == "sub"
        result.getUID() == "uid"
    }

    def "test getFellowPassengerList"() {
        given:
        HotelBookPassengerInput passengerInfo1 = new HotelBookPassengerInput()
        passengerInfo1.hotelPassengerInput = new HotelPassengerInput()
        passengerInfo1.hotelPassengerInput.approvalInput = new ApprovalInput()
        passengerInfo1.setName("name name 1");
        ApprovalInput approvalInput1 = new ApprovalInput();
        approvalInput1.setMasterApprovalNo("114514")
        approvalInput1.setSubApprovalNo("real no2")
        passengerInfo1.hotelPassengerInput.setApprovalInput(approvalInput1)
        EmailInfo emailInfo1 = new EmailInfo()
        emailInfo1.setTransferEmail("<EMAIL>")
        passengerInfo1.setEmailInfo(emailInfo1)
        PhoneInfo phoneInfo1 = new PhoneInfo()
        phoneInfo1.setTransferPhoneNo("*********0")
        phoneInfo1.setCountryCode("81")
        passengerInfo1.setPhoneInfo(phoneInfo1)
        CertificateInfo certificateInfo1 = new CertificateInfo()
        certificateInfo1.setCertificateNo("*********")
        certificateInfo1.setCertificateType("PASSPORT")
        passengerInfo1.setCertificateInfo(certificateInfo1)
        passengerInfo1.hotelPassengerInput.uid = "789"
        passengerInfo1.hotelPassengerInput.employee = "T"

        HotelBookPassengerInput passengerInfo2 = new HotelBookPassengerInput()
        passengerInfo2.hotelPassengerInput = new HotelPassengerInput()
        passengerInfo2.hotelPassengerInput.approvalInput = new ApprovalInput()
        passengerInfo2.setName("name name 2");
        ApprovalInput approvalInput2 = new ApprovalInput();
        approvalInput2.setMasterApprovalNo("415441")
        approvalInput2.setSubApprovalNo("real no")
        passengerInfo2.hotelPassengerInput.setApprovalInput(approvalInput2)
        EmailInfo emailInfo2 = new EmailInfo()
        emailInfo2.setTransferEmail("<EMAIL>")
        passengerInfo2.setEmailInfo(emailInfo2)
        PhoneInfo phoneInfo2 = new PhoneInfo()
        phoneInfo2.setTransferPhoneNo("9876543210")
        phoneInfo2.setCountryCode("852")
        passengerInfo2.setPhoneInfo(phoneInfo2)
        CertificateInfo certificateInfo2 = new CertificateInfo()
        certificateInfo2.setCertificateNo("*********")
        certificateInfo2.setCertificateType("PASSPORT")
        passengerInfo2.setCertificateInfo(certificateInfo2)
        passengerInfo2.hotelPassengerInput.infoId = "345"
        List<HotelBookPassengerInput> hotelPassengerInfos = Arrays.asList(passengerInfo1, passengerInfo2);

        when:
        def result = myTestClass.getFellowPassengerList(hotelPassengerInfos, null, null)

        then:
        result.size() == 2
        result.get(0).getUID() == "789";
        result.get(0).getNonEmployeeId() == null;
        result.get(0).getName() == "name name 1"
        result.get(0).getNameEn() == ""
        result.get(0).getApprovalNumber() == "real no2"

        result.get(1).getUID() == null;
        result.get(1).getNonEmployeeId() == "345";
        result.get(1).getName() == "name name 2"
        result.get(1).getNameEn() == ""
        result.get(1).getApprovalNumber() == "real no"
    }

    def "test getOrderInfo"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()

        CityInput cityInput = new CityInput();
        cityInput.setCityId(2);
        orderCreateRequestType.setCityInput(cityInput);

        HotelBookInput hotelBookInput = new HotelBookInput();
        HotelDateRangeInfo hotelDateRangeInfo = new HotelDateRangeInfo();
        hotelDateRangeInfo.setCheckIn("checkin");
        hotelDateRangeInfo.setCheckOut("2024-08-12")
        hotelBookInput.setHotelDateRangeInfo(hotelDateRangeInfo);
        orderCreateRequestType.setHotelBookInput(hotelBookInput);

        when:
        def result = myTestClass.getOrderInfo(orderCreateRequestType)

        then:
        result.getOrderId() == null;
        result.getCityID() == 2
        result.getEndDate() == "2024-08-12"
        result.getStartDate() == "checkin"
    }

    def "check"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                cityInput: new CityInput(cityId: 1),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "1", userId: "2")),
                hotelPolicyInput: new HotelPolicyInput(approvalInput: new ApprovalInput(subApprovalNo: "policysub"), policyInput: new PolicyInput(policyUid: "uid")),
                hotelBookPassengerInputs: Arrays.asList(
                        new HotelBookPassengerInput(
                                passengerBasicInfo: new PassengerBasicInfo(preferFirstName: "san", preferLastName: "zhang"),
                                hotelPassengerInput: new HotelPassengerInput(uid: "uid", employee: "T", approvalInput: new ApprovalInput(subApprovalNo: "sub"))),
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "6786", employee: "F"))),
        )
        /*WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("RepeatOrderControlCorp", "T")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();*/
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo)
        accountInfo.preApprovalSameTrip(_ as Boolean, _ as CorpPayInfo) >> true
        accountInfo.isPolicyPsgSameTrip() >> true
        accountInfo.isPolicyModel() >> true
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(balanceType: "PP"))
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvail =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        when:
        def result = myTestClass.check(Tuple4.of(orderCreateRequestType, accountInfo, checkAvail, null) as Tuple4<OrderCreateRequestType, WrapperOfAccount.AccountInfo, WrapperOfCheckAvail.CheckAvailContextInfo, QconfigOfCertificateInitConfig>)
        then:
        def ex = thrown(BusinessException)
        ex.errorCode == 497


        when:
        orderCreateRequestType.hotelBookPassengerInputs.get(1).hotelPassengerInput.approvalInput = new ApprovalInput(subApprovalNo: "1sub")
        result = myTestClass.check(Tuple4.of(orderCreateRequestType, accountInfo, checkAvail, null) as Tuple4<OrderCreateRequestType, WrapperOfAccount.AccountInfo, WrapperOfCheckAvail.CheckAvailContextInfo, QconfigOfCertificateInitConfig>)
        then:
        result != null
        result.friendlyMessage == "出行人zhang/san关联审批单状态变更，请返回重新选择"
    }
}