package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig
import com.ctrip.corp.bff.tools.contract.DataInfo
import com.ctrip.corp.corpsz.preapprovalws.common.contract.ResponseStatus
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFellowPassengerInfo
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFellowPassengerResponseType
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFellowResult
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFieldResult
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/14
 */
class MapperOfVerifyFellowResultResponseTest extends Specification {
    def myTestClass = new MapperOfVerifyFellowResultResponse()

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    def "test getVerifyResults"() {
        given:
        List<VerifyFieldResult> verifyFieldResults = new ArrayList<>();

        when:
        def result = myTestClass.getVerifyResults(verifyFieldResults)

        then:
        result.size() == 0


        when:
        verifyFieldResults.add(new VerifyFieldResult())
        result = myTestClass.getVerifyResults(verifyFieldResults)

        then:
        result.size() == 1
        result.get(0).getMatched() == "F"
        result.get(0).getElementInfo().getType() == null


        when:
        verifyFieldResults.add(null);
        result = myTestClass.getVerifyResults(verifyFieldResults)

        then:
        result.size() == 1

        when:
        verifyFieldResults = new ArrayList<>();

        def verifyFieldResult = new VerifyFieldResult()
        verifyFieldResult.setMatched(true)
        verifyFieldResult.setFieldName("endDate")
        verifyFieldResults.add(verifyFieldResult)

        result = myTestClass.getVerifyResults(verifyFieldResults)

        then:
        result.size() == 1
        result.get(0).getMatched() == "T"
        result.get(0).getElementInfo().getType() == "CHECK_OUT"
    }

    def "test convert"() {
        def responseStatus = new ResponseStatus();
        def responseType = new VerifyFellowPassengerResponseType();
        responseType.setStatus(responseStatus)
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(cityInput: new CityInput(cityId: 22249))
        orderCreateRequestType.hotelBookPassengerInputs = new ArrayList<>()
        orderCreateRequestType.hotelBookPassengerInputs.add(new HotelBookPassengerInput(
                hotelPassengerInput: new HotelPassengerInput(uid: "uid"),
                name: "name1"))
        orderCreateRequestType.hotelBookPassengerInputs.add(new HotelBookPassengerInput(
                hotelPassengerInput: new HotelPassengerInput(uid: "uid2"),
                name: "name2"))
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(gdsType: "", balanceType: "PP"))
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        Tuple4<VerifyFellowPassengerResponseType, OrderCreateRequestType, WrapperOfCheckAvail.CheckAvailContextInfo, QconfigOfCertificateInitConfig> tuple4 = Tuple4.of(responseType, orderCreateRequestType, checkAvailInfo, null)
        when:
        def result = myTestClass.convert(tuple4)

        then:
        def ex = thrown(BusinessException)
        ex.errorCode == 641


        when:
        responseStatus.setErrorCode(10303000)
        result = myTestClass.convert(tuple4)

        then:
        result.getT1() == false


        when:
        responseStatus.setErrorCode(10303016)
        result = myTestClass.convert(tuple4)

        then:
        ex = thrown(BusinessException)
        ex.errorCode == 642


        when:
        responseStatus.setErrorCode(103110002)
        List<VerifyFellowResult> verifyResults = new ArrayList<>();

        VerifyFellowResult verifyFellowResult1 = new VerifyFellowResult();
        VerifyFellowPassengerInfo passengerInfo1 = new VerifyFellowPassengerInfo()
        passengerInfo1.uID = "uid"
        passengerInfo1.setName("name1")
        verifyFellowResult1.setPassengerInfo(passengerInfo1)
        verifyResults.add(verifyFellowResult1)

        VerifyFellowResult verifyFellowResult2 = new VerifyFellowResult();
        VerifyFellowPassengerInfo passengerInfo2 = new VerifyFellowPassengerInfo()
        passengerInfo2.uID = "uid2"
        passengerInfo2.setName("name2")
        verifyFellowResult2.setPassengerInfo(passengerInfo2)
        verifyResults.add(verifyFellowResult2)

        responseType.setVerifyResult(verifyResults)
        result = myTestClass.convert(tuple4)

        then:
        ex = thrown(BusinessException)
        ex.errorCode == 643

        when:
        responseStatus.setErrorCode(103110003)
        result = myTestClass.convert(tuple4)

        then:
        result.getT2().getVerifyFellowInfo().getVerifyFellowPassengerInfos().get(0).getVerifyPassengerInfo().getUseName() == "name1"
    }
}
