package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant
import com.ctrip.corp.bff.hotel.book.common.enums.HotelGuaranteeTypeEnum
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.hotel.book.contract.TripInfoInput
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType
import com.ctrip.corp.hotelbook.commonws.entity.GuaranteeMethodInfoType
import mockit.Mock
import mockit.MockUp
import spock.lang.Specification
import spock.lang.Unroll
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.framework.hotel.entity.contract.OriginalOrderInput

/**
 * <AUTHOR>
 * @date 2024/12/9 11:31
 *
 */
class BookingInitUtilTest extends Specification {
    @Unroll
    def "testBuildSelectGuaranteeType with different scenarios"() {
        given:
        List<HotelPayTypeInput> hotelPayTypeInputs = hotelPayTypeInputList

        when: "calling buildSelectGuaranteeType with specific parameters"
        def result = BookingInitUtil.buildSelectGuaranteeType(hotelPayTypeInputs)

        then: "the result should be as expected"
        result == expectedResult

        where:
        hotelPayTypeInputList                                                    || expectedResult
        [new HotelPayTypeInput(payCode: "GUARANTEE", payType: "CORP_GUARANTEE")] || HotelGuaranteeTypeEnum.CORP_GUARANTEE
        [new HotelPayTypeInput(payCode: "GUARANTEE", payType: "SELF_GUARANTEE")] || HotelGuaranteeTypeEnum.SELF_GUARANTEE
        [new HotelPayTypeInput(payCode: "OTHER", payType: "OTHER_TYPE")]         || null
        [new HotelPayTypeInput(payCode: "GUARANTEE", payType: "UNKNOWN_TYPE")]   || HotelGuaranteeTypeEnum.NONE
        []                                                                       || null
    }

    @Unroll
    def "testBuildDefaultGuaranteeType with different scenarios"() {
        given:
        List<GuaranteeMethodInfoType> guaranteeMethodInfoTypes = guaranteeMethodInfoTypeList
        HotelGuaranteeTypeEnum selectGuaranteeType = selectedGuaranteeType

        when: "calling buildDefaultGuaranteeType with specific parameters"
        def result = BookingInitUtil.buildDefaultGuaranteeType(guaranteeMethodInfoTypes, selectGuaranteeType)

        then: "the result should be as expected"
        result == expectedResult

        where:
        guaranteeMethodInfoTypeList                                                                                                               || selectedGuaranteeType                 || expectedResult
        [new GuaranteeMethodInfoType(guaranteeMethod: "ACCOUNT_GUARANTEE")]                                                                       || HotelGuaranteeTypeEnum.CORP_GUARANTEE || HotelGuaranteeTypeEnum.CORP_GUARANTEE
        [new GuaranteeMethodInfoType(guaranteeMethod: "INDIVIDUAL_GUARANTEE")]                                                                    || HotelGuaranteeTypeEnum.SELF_GUARANTEE || HotelGuaranteeTypeEnum.SELF_GUARANTEE
        [new GuaranteeMethodInfoType(guaranteeMethod: "ACCOUNT_GUARANTEE"), new GuaranteeMethodInfoType(guaranteeMethod: "INDIVIDUAL_GUARANTEE")] || HotelGuaranteeTypeEnum.SELF_GUARANTEE || HotelGuaranteeTypeEnum.SELF_GUARANTEE
        [new GuaranteeMethodInfoType(guaranteeMethod: "ACCOUNT_GUARANTEE"), new GuaranteeMethodInfoType(guaranteeMethod: "INDIVIDUAL_GUARANTEE")] || HotelGuaranteeTypeEnum.NONE           || HotelGuaranteeTypeEnum.CORP_GUARANTEE
        [new GuaranteeMethodInfoType(guaranteeMethod: "ACCOUNT_GUARANTEE")]                                                                       || HotelGuaranteeTypeEnum.NONE           || HotelGuaranteeTypeEnum.CORP_GUARANTEE
        []                                                                                                                                        || HotelGuaranteeTypeEnum.CORP_GUARANTEE || null
    }

    def "testHasGuaGuaranteeType"() {
        given:
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType = new GetSupportedPaymentMethodResponseType()
        getSupportedPaymentMethodResponseType.setGuaranteeMethodList(guaranteeMethodInfoTypes)
        when: "calling buildDefaultGuaranteeType with specific parameters"
        def result = BookingInitUtil.hasGuaGuaranteeType(getSupportedPaymentMethodResponseType)

        then: "the result should be as expected"
        result == expectedResult

        where:
        guaranteeMethodInfoTypes                                                                                                                    || expectedResult
        [new GuaranteeMethodInfoType(guaranteeMethod: "ACCOUNT_GUARANTEE")]                                                                         || true
        [new GuaranteeMethodInfoType(guaranteeMethod: "INDIVIDUAL_GUARANTEE")]                                                                      || true
        [new GuaranteeMethodInfoType(guaranteeMethod: "ACCOUNT_GUARANTEE1"), new GuaranteeMethodInfoType(guaranteeMethod: "INDIVIDUAL_GUARANTEE")]  || true
        [new GuaranteeMethodInfoType(guaranteeMethod: "ACCOUNT_GUARANTEE1"), new GuaranteeMethodInfoType(guaranteeMethod: "INDIVIDUAL_GUARANTEE1")] || false
        [new GuaranteeMethodInfoType(guaranteeMethod: "ACCOUNT_GUARANTEE")]                                                                         || true
        []                                                                                                                                          || false
    }

    def "testBuildTripInput with default scenario"() {
        given:
        TripInfoInput tripInfoInput = new TripInfoInput(tripId: "trip123")

        when:
        TripInput result = BookingInitUtil.buildTripInput(tripInfoInput)

        then:
        result?.tripId == "trip123"
    }


    @Unroll
    def "testGetBffRoomPayType with #description"() {
        given: "A default payment, hotel booking filter policy, and pay list"
        // No additional setup required

        when: "Calling getBffRoomPayType"
        def result = BookingInitUtil.getBffRoomPayType(defaultPayment, hotelBookingFilterByPolicy, payList)

        then: "The result should match the expected value"
        result == expectedResult

        where:
        description                   | defaultPayment            | hotelBookingFilterByPolicy | payList                           || expectedResult
        "UNION_PAY available"         | HotelPayTypeEnum.CORP_PAY | null                       | [HotelPayTypeEnum.UNION_PAY]      || HotelPayTypeEnum.UNION_PAY
        "Empty pay list"              | HotelPayTypeEnum.CORP_PAY | null                       | []                                || HotelPayTypeEnum.NONE
        "Default payment in pay list" | HotelPayTypeEnum.CORP_PAY | null                       | [HotelPayTypeEnum.CORP_PAY]       || HotelPayTypeEnum.CORP_PAY
        "FLASH_STAY_PAY available"    | HotelPayTypeEnum.SELF_PAY | null                       | [HotelPayTypeEnum.FLASH_STAY_PAY] || HotelPayTypeEnum.FLASH_STAY_PAY
        "MIX_PAY with default policy" | HotelPayTypeEnum.MIX_PAY  | "S"                        | [HotelPayTypeEnum.MIX_PAY]        || HotelPayTypeEnum.MIX_PAY
        "PRBAL available"             | HotelPayTypeEnum.SELF_PAY | null                       | [HotelPayTypeEnum.PRBAL]          || HotelPayTypeEnum.PRBAL
        "CORP_PAY fallback"           | HotelPayTypeEnum.SELF_PAY | null                       | [HotelPayTypeEnum.CORP_PAY]       || HotelPayTypeEnum.CORP_PAY
        "SELF_PAY fallback"           | HotelPayTypeEnum.SELF_PAY | null                       | [HotelPayTypeEnum.SELF_PAY]       || HotelPayTypeEnum.SELF_PAY
        "First non-null in pay list"  | HotelPayTypeEnum.SELF_PAY | null                       | [null, HotelPayTypeEnum.CASH]     || HotelPayTypeEnum.CASH
    }

    @Unroll
    def "testBuildPayTypeEnum with #description"() {
        given: "A payTypeEnum string"
        // No additional setup required

        when: "Calling buildPayTypeEnum"
        def result = BookingInitUtil.buildPayTypeEnum(payTypeEnum)

        then: "The result should match the expected value"
        result == expectedResult

        where:
        description                     | payTypeEnum          || expectedResult
        "CORP_PAY scenario"             | "CORP_PAY"          || HotelPayTypeEnum.CORP_PAY
        "ACCOUNT_PAY scenario"          | "ACCOUNT_PAY"       || HotelPayTypeEnum.CORP_PAY
        "SELF_PAY scenario"             | "SELF_PAY"          || HotelPayTypeEnum.SELF_PAY
        "INDIVIDUAL_PAY scenario"       | "INDIVIDUAL_PAY"    || HotelPayTypeEnum.SELF_PAY
        "MIX_PAY scenario"              | "MIX_PAY"           || HotelPayTypeEnum.MIX_PAY
        "CASH_PAY scenario"             | "CASH_PAY"          || HotelPayTypeEnum.CASH
        "UNION_PAY scenario"            | "UNION_PAY"         || HotelPayTypeEnum.UNION_PAY
        "FLASH_STAY_PAY scenario"       | "FLASH_STAY_PAY"    || HotelPayTypeEnum.FLASH_STAY_PAY
        "PRBAL scenario"                | "PRBAL"             || HotelPayTypeEnum.PRBAL
        "Unknown payTypeEnum scenario"  | "UNKNOWN_PAY"       || HotelPayTypeEnum.NONE
        "Null payTypeEnum scenario"     | null                || HotelPayTypeEnum.NONE
        "Empty payTypeEnum scenario"    | ""                  || HotelPayTypeEnum.NONE
    }


    def "getDefaultServicePayment" () {
        expect:
        BookingInitUtil.getDefaultServicePayment(null, null, [HotelPayTypeEnum.PRBAL], [null]) == HotelPayTypeEnum.PRBAL
    }


    @Unroll
    def "testBuildBlueSpaceCostCenterNew with #description"() {
        given: "A mocked QConfigOfCustomConfig and a BookingInitRequestType"
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if ("compare".equalsIgnoreCase(corpId) && "blueSpaceCostCenterNewCompare".equalsIgnoreCase(key)) {
                    return true
                }
                if ("use".equalsIgnoreCase(corpId) && "blueSpaceCostCenterNewUse".equalsIgnoreCase(key)) {
                    return true
                }
                return false
            }
        }
        def requestType = Mock(BookingInitRequestType)
        def integrationSoaRequestType = Mock(IntegrationSoaRequestType)
        def userInfo = Mock(UserInfo)
        requestType.getIntegrationSoaRequestType() >> integrationSoaRequestType
        integrationSoaRequestType.getUserInfo() >> userInfo
        userInfo.getCorpId() >> userCorpId
        expect: "Calling buildBlueSpaceCostCenterNew"
        result == BookingInitUtil.buildBlueSpaceCostCenterNew(requestType)
        where:
        description            | userCorpId | result
        "COMPARE is supported" | "use"      | "USE_NEW"
        "USE is supported"     | "compare"  | "COMPARE"
        "Neither is supported" | "close"    | "USE_OLD"
    }

    @Unroll
    def "approvalFlowReuseNew - #desc"() {
        given:
        def bookingInitRequestType = Mock(BookingInitRequestType)
        bookingInitRequestType.getOriginalOrderInput() >> originalOrderInput
        if (originalOrderInput != null) {
            originalOrderInput.getOriginalOrderId() >> originalOrderId
        }
        GroovyMock(StrategyOfBookingInitUtil, global: true)
        StrategyOfBookingInitUtil.approvalReuseReBook(strategyInfoMap) >> approvalReuseReBookResult

        when:
        def result = BookingInitUtil.approvalFlowReuseNew(bookingInitRequestType, strategyInfoMap)

        then:
        result == expected

        where:
        desc                                      | originalOrderInput         | originalOrderId | strategyInfoMap | approvalReuseReBookResult | expected
        "原单为空，策略返回false，返回false"         | null                      | null            | [:]            | false                    | false
        "原单不为空，id为空，返回false"             | Mock(OriginalOrderInput)  | null            | [:]            | false                    | false
        "原单不为空，id为''，返回false"             | Mock(OriginalOrderInput)  | ""              | [:]            | false                    | false
        "原单不为空，id为全空格，返回false"         | Mock(OriginalOrderInput)  | "   "           | [:]            | false                    | false
        "原单不为空，id不为空，策略返回false，返回false" | Mock(OriginalOrderInput)  | "123"           | [:]            | false                    | false
    }
}
