package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.TeamRoomInfo
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;

class MapperOfTeamRoomConfirmInfoResponseTest extends Specification {


    def tester = Spy(new MapperOfTeamRoomConfirmInfoResponse())

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        !tester.convert(Tuple4.of(new OrderCreateToken(), new OrderCreateRequestType( ), Mock( WrapperOfCheckAvail.BaseCheckAvailInfo), new QconfigOfCertificateInitConfig())).t1
        !tester.convert(Tuple4.of(new OrderCreateToken(continueTypes: [ContinueTypeConst.TEAM_ROOM]), new OrderCreateRequestType( teamRoomInfo: new TeamRoomInfo()), Mock( WrapperOfCheckAvail.BaseCheckAvailInfo), new QconfigOfCertificateInitConfig())).t1
        !tester.convert(Tuple4.of(new OrderCreateToken(continueTypes: []), new OrderCreateRequestType( teamRoomInfo: new TeamRoomInfo(teamRoom: false)), Mock( WrapperOfCheckAvail.BaseCheckAvailInfo), new QconfigOfCertificateInitConfig())).t1
        !tester.convert(Tuple4.of(new OrderCreateToken(continueTypes: []), new OrderCreateRequestType( teamRoomInfo: new TeamRoomInfo(teamRoom: true)), Mock( WrapperOfCheckAvail.BaseCheckAvailInfo), new QconfigOfCertificateInitConfig())).t1
    }
}
